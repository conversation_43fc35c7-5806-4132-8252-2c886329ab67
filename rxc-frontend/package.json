{"name": "rxc-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts --fix", "format": "prettier --write ."}, "dependencies": {"@tabler/icons-vue": "^3.34.1", "@tailwindcss/vite": "^4.1.12", "@tanstack/vue-table": "^8.21.3", "@tiptap/extension-color": "^3.4.1", "@tiptap/extension-list-item": "^3.4.1", "@tiptap/extension-text-align": "^3.4.1", "@tiptap/extension-text-style": "^3.4.1", "@tiptap/extension-underline": "^3.4.1", "@tiptap/starter-kit": "^3.4.1", "@tiptap/vue-3": "^3.4.1", "@types/js-cookie": "^3.0.6", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.7.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "lucide-vue-next": "^0.540.0", "maska": "^3.2.0", "pinia": "^3.0.3", "qrcode.vue": "^3.6.0", "reka-ui": "^2.4.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "vee-validate": "^4.15.1", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-sonner": "^2.0.8", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.34.0", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}