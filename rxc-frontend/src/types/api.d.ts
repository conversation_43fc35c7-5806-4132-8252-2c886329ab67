export interface User {
  id: string | number
  email: string
  name: string
}

export type ApiResponse = {
  status: number
  message?: string
  errors?: Record<string, string[]>
}

export type PaginationResponse<T> = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: T[]
}

export type AppSettings = {
  id?: number
  app_name?: string
  app_logo?: string
  app_favicon?: string
  lp_data?: LandingPageData
}

export type LandingPageData = {
  topBanner?: {
    items: {
      text: string
      icon: string
    }[]
  }
  hero: {
    phrases: string[]
    title: string
    subtitle: string
    features: {
      text: string
    }[]
  }
  heroCallout: {
    title: string
    buttonText: string
    backgroundImage: string
    newBackgroundImage?: string
    buttonLink: string
  }
  features: {
    title: string
    subtitle: string
    featureList: {
      title: string
      description: string
      icon: string
    }[]
  }
  productHighlights: {
    title: string
    buttonText: string
    buttonLink: string
  }
  testimonials: {
    title: string
    subtitle: string
    testimonialList: {
      name: string
      review: string
    }[]
  }
  fixItTogether: {
    title: string
    cards: {
      tag: string
      title: string
      backgroundImage: string
      newBackgroundImage?: string
      link: string
    }[]
  }
  howItWorks: {
    title: string
    subtitle: string
    steps: {
      step: string
      title: string
      description: string
      icon: string
      color: string
      iconBg: string
      iconColor: string
      stepColor: string
    }[]
    buttonText: string
    buttonLink: string
  }
  faq: {
    title: string
    subtitle: string
    faqList: {
      question: string
      answer: string
    }[]
  }
  cta: {
    title: string
    subtitle: string
    paragraph: string
    buttonText: string
    buttonLink: string
  }
}

export type Product = SingleProduct | ProgramProduct

type BaseProduct = {
  slug: string
  product_type: 'single' | 'programs'
  main_category_name: string
  sub_category_name: string | null
  image: string
  price: number
  product_name_title: string
}

type SingleProduct = BaseProduct & {
  product_type: 'single'
  product_form: string
  product_strength: string | null
  product_qty: string | null
  xpedicare_url: string
}

type ProgramProduct = BaseProduct & {
  product_type: 'programs'
  product_items: ProgramProductItem[]
}

type ProgramProductItem = {
  product_id: string
  product_name: string
  product_form: string
  price: number
  product_strength: string
  product_qty: string
  xpedicare_url: string
}

export type Category = {
  id: string
  name: string
  slug: string
  image: string
  description: string
  sub_categories: {
    id: string
    main_category_id: string
    name: string
    slug: string
  }[]
}

export type ProductDetails = SingleProductDetails | ProgramProductDetails

type BaseProductDetails = {
  slug: string
  product_type: 'single' | 'programs'
  image: string
  main_category_name: string
  main_category_slug: string
  sub_category_name: string | null
  price: number
  product_form: string
  product_strength: string | null
  product_qty: string | null
  product_name_title: string
  recommendProducts: RecommendProduct[]
  product_descriptions: {
    title: string
    description: string
  }[]
  product_faqs: {
    question: string
    answer: string
  }[]
}

type SingleProductDetails = BaseProductDetails & {
  product_type: 'single'
  xpedicare_url: string
}

type ProgramProductDetails = BaseProductDetails & {
  product_type: 'programs'
  product_items: ProgramProductItem[]
}

type RecommendProduct = {
  slug: string
  main_category_name: string
  sub_category_name: string | null
  image: string
  product_type: 'single' | 'programs'
  product_name_title: string
  product_form: string
  product_strength: string | null
  product_qty: string | null
  price: number
  xpedicare_url: string
}

type ProgramProductItem = {
  product_id: string
  product_name: string
  product_form: string
  product_strength: string
  product_qty: string
  price: number
  xpedicare_url: string
}

export type MainCategory = {
  id: string
  name: string
  slug: string
  sub_categories: SubCategory[]
  products: Product[]
}

export type SubCategory = {
  id: string
  main_category_id: string
  name: string
  slug: string
}
