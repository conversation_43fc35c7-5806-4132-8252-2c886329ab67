<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { storeToRefs } from 'pinia'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Settings2, Loader2, Image } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { useSettingsStore, appSettingSchema } from '@/stores'
import { isEmptyObject } from '@/lib'

const settingsStore = useSettingsStore()
const {
  settings,
  isLoading,
  error,
  fieldErrors,
  isUpdatingAppSetting,
  isUpdatingLogo,
  isUpdatingFavicon,
} = storeToRefs(settingsStore)
const { fetchSettings, updateAppSetting, updateLogo, updateFavicon } = settingsStore

const formError = ref<string | null>(null)
const logoInputRef = ref<HTMLInputElement | null>(null)
const faviconInputRef = ref<HTMLInputElement | null>(null)

function triggerLogoPicker() {
  logoInputRef.value?.click()
}

function triggerFaviconPicker() {
  faviconInputRef.value?.click()
}

function validateImageFile(file: File): boolean {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  if (!allowedTypes.includes(file.type.toLowerCase())) {
    toast.error('Please select a JPG, JPEG, or PNG image file')
    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    toast.error('Image size must be less than 3MB')
    return false
  }
  return true
}

async function handleLogoChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return
  if (!validateImageFile(file)) {
    if (logoInputRef.value) logoInputRef.value.value = ''
    return
  }
  const reader = new FileReader()
  reader.onload = async () => {
    const base64 = reader.result as string
    const success = await updateLogo({ image: base64 })
    if (success) {
      settings.value = { ...settings.value, app_logo: base64 }
    }
    if (logoInputRef.value) logoInputRef.value.value = ''
  }
  reader.onerror = () => {
    toast.error('Failed to read image file')
    if (logoInputRef.value) logoInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

async function handleFaviconChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return
  if (!validateImageFile(file)) {
    if (faviconInputRef.value) faviconInputRef.value.value = ''
    return
  }
  const reader = new FileReader()
  reader.onload = async () => {
    const base64 = reader.result as string
    const success = await updateFavicon({ image: base64 })
    if (success) {
      settings.value = { ...settings.value, app_favicon: base64 }
    }
    if (faviconInputRef.value) faviconInputRef.value.value = ''
  }
  reader.onerror = () => {
    toast.error('Failed to read image file')
    if (faviconInputRef.value) faviconInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

// Unified form
const form = useForm({
  validationSchema: toTypedSchema(appSettingSchema),
  initialValues: {
    app_name: '',
  },
})

function applyFieldErrors() {
  const errors = fieldErrors.value

  if (!errors || typeof errors !== 'object') return

  Object.entries(errors).forEach(([key, msgs]) => {
    const first = Array.isArray(msgs) ? msgs[0] : String(msgs)
    form.setFieldError(key as any, first)
  })
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isUpdatingAppSetting.value) return

  error.value = null
  fieldErrors.value = {}

  const result = await updateAppSetting({
    ...values,
    support_phone_number: values.support_phone_number?.replace(/\D/g, ''),
  })

  if (result) {
    toast.success('Settings updated successfully!')
  } else if (!isEmptyObject(fieldErrors.value)) {
    applyFieldErrors()
  } else {
    error.value = error.value || 'Failed to update settings'
  }
})

watch(settings, (newSettings) => {
  if (newSettings) {
    form.setValues({
      app_name: newSettings.app_name || '',
    })
  }
})

onMounted(async () => {
  await fetchSettings()
})
</script>

<template>
  <div class="space-y-6 w-full">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <Settings2 class="h-6 w-6 text-primary" />
        <h1 class="text-2xl font-bold">App Settings</h1>
      </div>
      <!-- <Button type="submit" form="settings-form" :disabled="isUpdatingAppSetting" class="ml-auto">
        <Loader2 v-if="isUpdatingAppSetting" class="mr-2 h-4 w-4 animate-spin" />
        {{ isUpdatingAppSetting ? 'Saving...' : 'Save Changes' }}
      </Button> -->
    </div>

    <div v-if="isLoading">
      <Card>
        <CardContent class="flex items-center justify-center h-32">
          <Loader2 class="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    </div>

    <form v-else id="settings-form" class="space-y-6" @submit.prevent="onSubmit">
      <!-- Branding Section -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2 text-xl">
            <Settings2 class="h-6 w-6" />
            Branding
          </CardTitle>
          <CardDescription>Configure your application branding</CardDescription>
        </CardHeader>
        <CardContent class="space-y-8">
          <!-- Logo & Favicon Upload -->
          <div>
            <!-- <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
              <Settings2 class="h-5 w-5" />
              Branding
            </h3> -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField name="_app_logo">
                <FormItem>
                  <FormLabel>App Logo</FormLabel>
                  <FormControl>
                    <div class="flex items-center gap-4">
                      <div
                        class="h-20 w-36 rounded border overflow-hidden checkerboard flex items-center justify-center"
                      >
                        <template v-if="settings.app_logo">
                          <img
                            :src="settings.app_logo"
                            alt="Logo preview"
                            class="max-h-full max-w-full object-contain"
                          />
                        </template>
                        <template v-else>
                          <Image class="h-8 w-8 text-muted-foreground" />
                        </template>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        @click="triggerLogoPicker"
                        :disabled="isUpdatingLogo"
                      >
                        <Loader2 v-if="isUpdatingLogo" class="mr-2 h-4 w-4 animate-spin" />
                        {{ settings.app_logo ? 'Change Logo' : 'Select Logo' }}
                      </Button>
                      <input
                        ref="logoInputRef"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        class="hidden"
                        @change="handleLogoChange"
                      />
                    </div>
                  </FormControl>
                  <div class="mt-2 text-xs text-muted-foreground">
                    Recommended size: 200x50px. Formats: JPG, PNG. Max size: 3MB
                  </div>
                </FormItem>
              </FormField>

              <FormField name="_app_favicon">
                <FormItem>
                  <FormLabel>App Favicon</FormLabel>
                  <FormControl>
                    <div class="flex items-center gap-4">
                      <div
                        class="h-12 w-12 rounded border overflow-hidden checkerboard flex items-center justify-center"
                      >
                        <template v-if="settings.app_favicon">
                          <img
                            :src="settings.app_favicon"
                            alt="Favicon preview"
                            class="max-h-full max-w-full object-contain"
                          />
                        </template>
                        <template v-else>
                          <Image class="h-6 w-6 text-muted-foreground" />
                        </template>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        @click="triggerFaviconPicker"
                        :disabled="isUpdatingFavicon"
                      >
                        <Loader2 v-if="isUpdatingFavicon" class="mr-2 h-4 w-4 animate-spin" />
                        {{ settings.app_favicon ? 'Change Favicon' : 'Select Favicon' }}
                      </Button>
                      <input
                        ref="faviconInputRef"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        class="hidden"
                        @change="handleFaviconChange"
                      />
                    </div>
                  </FormControl>
                  <div class="mt-2 text-xs text-muted-foreground">
                    Recommended size: 32x32px. Formats: ICO, PNG. Max size: 3MB
                  </div>
                </FormItem>
              </FormField>
            </div>

            <!-- App Name -->
            <FormField name="app_name" v-slot="{ componentField, errorMessage }">
              <FormItem class="mt-4">
                <FormLabel>App Name</FormLabel>
                <FormControl>
                  <Input
                    v-bind="componentField"
                    placeholder="Enter app name"
                    :aria-invalid="!!errorMessage"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>
        </CardContent>
      </Card>

      <!-- Error Alert -->
      <Alert v-if="formError" variant="destructive" class="mt-6">
        <AlertDescription>{{ formError }}</AlertDescription>
      </Alert>

      <!-- Bottom Save Button -->
      <div class="flex justify-end pt-4">
        <Button type="submit" :disabled="isUpdatingAppSetting">
          <Loader2 v-if="isUpdatingAppSetting" class="mr-2 h-4 w-4 animate-spin" />
          {{ isUpdatingAppSetting ? 'Saving...' : 'Save Changes' }}
        </Button>
      </div>
    </form>
  </div>
</template>

<style scoped>
.checkerboard {
  background-color: hsl(var(--background));
  background-image:
    linear-gradient(45deg, rgba(0, 0, 0, 0.06) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(0, 0, 0, 0.06) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(0, 0, 0, 0.06) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(0, 0, 0, 0.06) 75%);
  background-size: 16px 16px;
  background-position:
    0 0,
    0 8px,
    8px -8px,
    -8px 0px;
}

:deep(.dark) .checkerboard {
  background-color: hsl(var(--background));
  background-image:
    linear-gradient(45deg, rgba(255, 255, 255, 0.08) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.08) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.08) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.08) 75%);
}
</style>
