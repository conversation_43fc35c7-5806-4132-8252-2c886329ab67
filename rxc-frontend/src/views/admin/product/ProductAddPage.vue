<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useForm, FieldArray } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  ArrowLeft,
  Plus,
  Trash2,
  Image,
  Loader2,
  Package,
  Layers,
  CheckCircle,
} from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { productPayloadSchema, useProductStore, type ProductPayload } from '@/stores'
import { storeToRefs } from 'pinia'
import { toast } from 'vue-sonner'
import { isEmptyObject, scrollToTop } from '@/lib'

const productStore = useProductStore()
const {
  error: storeError,
  isAdding,
  fieldErrors,
  categoryOptions,
  isLoadingCategoryOptions,
} = storeToRefs(productStore)
const { addProduct, fetchCategoryOptions } = productStore

const router = useRouter()
const formError = ref('')
const fileInputRef = ref<HTMLInputElement | null>(null)

const formSchema = toTypedSchema(productPayloadSchema)

const form = useForm<ProductPayload>({
  validationSchema: formSchema,
  initialValues: {
    product_type: 'single',
    main_category_id: '',
    sub_category_id: '',
    image: '',
    // Single product fields
    product_name: '',
    product_qty: '',
    product_form: '',
    product_strength: '',
    price: '',
    xpedicare_url: '',
    // Program product fields
    product_title: '',
    product_items: [],
  } as ProductPayload,
})

const selectedMainCategoryId = computed(() => form.values.main_category_id)
watch(selectedMainCategoryId, async (newCategoryId) => {
  if (newCategoryId) {
    form.setFieldValue('sub_category_id', '')
  }
})

const subCategoryOptions = computed(() => {
  const mainCategoryId = form.values.main_category_id
  return categoryOptions.value.find((c) => c.id === mainCategoryId)?.sub_categories || []
})

// Watch for product type changes to reset form
const productType = computed(() => form.values.product_type)
watch(productType, (newType) => {
  if (newType === 'single') {
    form.setFieldValue('product_title', '')
    form.setFieldValue('product_items', [])
  } else if (newType === 'programs') {
    form.setFieldValue('product_name', '')
    form.setFieldValue('product_qty', '')
    form.setFieldValue('product_form', '')
    form.setFieldValue('product_strength', '')
    form.setFieldValue('price', '')
    form.setFieldValue('xpedicare_url', '')

    // Add a default item
    form.setFieldValue('product_items', [
      {
        product_name: '',
        product_qty: '',
        product_form: '',
        product_strength: '',
        price: '',
        xpedicare_url: '',
      },
    ])
  }
})

function triggerImagePicker() {
  fileInputRef.value?.click()
}

function handleImageChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return

  // Validate file type (only JPG/JPEG/PNG)
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  if (!allowedTypes.includes(file.type.toLowerCase())) {
    toast.error('Please select a JPG, JPEG, or PNG image file')
    // Reset input
    if (fileInputRef.value) fileInputRef.value.value = ''
    return
  }

  if (file.size > 3 * 1024 * 1024) {
    toast.error('Image size must be less than 3MB')
    // Reset input
    if (fileInputRef.value) fileInputRef.value.value = ''
    return
  }

  const reader = new FileReader()
  reader.onload = () => {
    const base64 = reader.result as string
    form.setFieldValue('image', base64)
    form.validateField('image')
    // Reset input
    if (fileInputRef.value) fileInputRef.value.value = ''
  }
  reader.onerror = () => {
    toast.error('Failed to read image file')
    // Reset input
    if (fileInputRef.value) fileInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

function addProgramItem() {
  const currentValues = form.values as any
  const currentItems = currentValues.product_items || []
  form.setFieldValue('product_items', [
    ...currentItems,
    {
      product_name: '',
      product_qty: '',
      product_form: '',
      product_strength: '',
      price: '',
      xpedicare_url: '',
    },
  ])
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isAdding.value) return
  formError.value = ''

  const result = await addProduct(values)

  if (result.success) {
    router.replace({ name: 'admin-products-overview', params: { id: result.id } })
  } else if (!isEmptyObject(fieldErrors.value)) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof ProductPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to add product. Please try again.'
  }

  scrollToTop()
})

onMounted(async () => {
  await fetchCategoryOptions()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Add Product</h1>
    </div>

    <Card>
      <CardContent class="pt-6">
        <form class="space-y-8" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Product Type Selection -->
          <div>
            <h2 class="text-lg font-medium mb-4">Product Type</h2>
            <FormField name="product_type" v-slot="{ field }">
              <FormItem>
                <FormControl>
                  <RadioGroup
                    :model-value="field.value"
                    @update:model-value="field.onChange($event)"
                    class="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <!-- Single Product Option -->
                    <div class="relative">
                      <RadioGroupItem value="single" id="single-add" class="peer sr-only" />
                      <label
                        for="single-add"
                        class="flex flex-col items-start p-4 border-2 border-muted rounded-lg cursor-pointer transition-all hover:border-primary/50 relative"
                        :class="{
                          'border-primary bg-primary/5': form.values.product_type === 'single',
                        }"
                      >
                        <div class="flex items-center justify-between w-full mb-2">
                          <div class="flex items-center gap-3">
                            <Package class="h-5 w-5 text-primary" />
                            <span class="font-medium">Single Product</span>
                          </div>
                          <CheckCircle
                            v-if="form.values.product_type === 'single'"
                            class="h-5 w-5 text-primary"
                          />
                        </div>
                        <p class="text-sm text-muted-foreground text-left">
                          Create a single product with one set of details, pricing, and
                          specifications.
                        </p>
                        <div class="mt-2 text-xs text-muted-foreground">
                          Best for: Individual medications, supplements, or standalone products
                        </div>
                      </label>
                    </div>

                    <!-- Program Option -->
                    <div class="relative">
                      <RadioGroupItem value="programs" id="programs-add" class="peer sr-only" />
                      <label
                        for="programs-add"
                        class="flex flex-col items-start p-4 border-2 border-muted rounded-lg cursor-pointer transition-all hover:border-primary/50 relative"
                        :class="{
                          'border-primary bg-primary/5': form.values.product_type === 'programs',
                        }"
                      >
                        <div class="flex items-center justify-between w-full mb-2">
                          <div class="flex items-center gap-3">
                            <Layers class="h-5 w-5 text-primary" />
                            <span class="font-medium">Program (Multiple Products)</span>
                          </div>
                          <CheckCircle
                            v-if="form.values.product_type === 'programs'"
                            class="h-5 w-5 text-primary"
                          />
                        </div>
                        <p class="text-sm text-muted-foreground text-left">
                          Create a program containing multiple related products with different
                          specifications and pricing.
                        </p>
                        <div class="mt-2 text-xs text-muted-foreground">
                          Best for: Treatment packages, product bundles, or multi-step programs
                        </div>
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <!-- Basic Information Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Basic Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- Main Category -->
              <FormField name="main_category_id" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Main Category *</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="isLoadingCategoryOptions"
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectValue placeholder="Select main category" />
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="isLoadingCategoryOptions" class="p-2 text-center text-sm">
                          Loading categories...
                        </div>
                        <div
                          v-else-if="categoryOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No categories available
                        </div>
                        <SelectItem
                          v-for="category in categoryOptions"
                          :key="category.id"
                          :value="category.id"
                        >
                          {{ category.name }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Sub Category -->
              <FormField name="sub_category_id" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Sub Category (Optional)</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="!selectedMainCategoryId"
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectValue placeholder="Select sub category" />
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="!selectedMainCategoryId" class="p-2 text-center text-sm">
                          Please select a main category first
                        </div>
                        <div
                          v-else-if="subCategoryOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No sub-categories available
                        </div>
                        <SelectItem
                          v-for="subCategory in subCategoryOptions"
                          :key="subCategory.id"
                          :value="subCategory.id"
                        >
                          {{ subCategory.name }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Product Image Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Product Image</h2>
            <FormField name="image">
              <FormItem>
                <div class="flex items-center gap-4">
                  <img
                    v-if="form.values.image"
                    :src="form.values.image"
                    alt="Selected image preview"
                    class="h-20 w-20 rounded object-cover border"
                  />
                  <div v-else class="h-20 w-20 rounded border flex items-center justify-center">
                    <Image class="h-8 w-8 text-muted-foreground" />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    :disabled="isAdding"
                    @click="triggerImagePicker"
                  >
                    Select Image
                  </Button>
                  <input
                    ref="fileInputRef"
                    type="file"
                    accept="image/jpeg,image/jpg,image/png"
                    class="hidden"
                    @change="handleImageChange"
                  />
                </div>

                <!-- Help Text -->
                <div class="mt-2 text-xs text-muted-foreground">
                  Supported formats: JPG, JPEG, PNG. Max size: 3MB.
                </div>

                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <!-- Single Product Fields -->
          <div v-if="form.values.product_type === 'single'">
            <h2 class="text-lg font-medium mb-4">Product Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- Product Name -->
              <FormField name="product_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Name *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter product name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Product Quantity -->
              <FormField name="product_qty" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Quantity</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter quantity (e.g., 30 tablets)"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Product Form -->
              <FormField name="product_form" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Form</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter form (e.g., tablet, capsule)"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Product Strength -->
              <FormField name="product_strength" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Strength</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter strength (e.g., 500mg)"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Price -->
              <FormField name="price" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Price *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter price"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Xpedicare URL -->
              <FormField name="xpedicare_url" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Xpedicare URL *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="url"
                      placeholder="https://example.com"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Program Product Fields -->
          <div v-if="form.values.product_type === 'programs'">
            <h2 class="text-lg font-medium mb-4">Program Details</h2>

            <!-- Program Title -->
            <div class="mb-6">
              <FormField name="product_title" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Program Title *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter program title"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>

            <!-- Program Items -->
            <div>
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-md font-medium">Program Items</h3>
              </div>

              <FieldArray name="product_items" v-slot="{ fields, remove }">
                <div v-if="fields.length === 0" class="text-center py-8 text-muted-foreground">
                  No items added yet. Click "Add Item" to get started.
                </div>

                <div
                  v-for="(field, index) in fields"
                  :key="field.key"
                  class="border rounded-lg p-4 mb-4"
                >
                  <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium">Item {{ index + 1 }}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      @click="remove(index)"
                      class="text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Product Name -->
                    <FormField
                      :name="`product_items.${index}.product_name`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Name *</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter product name"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Product Quantity -->
                    <FormField
                      :name="`product_items.${index}.product_qty`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Quantity</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter quantity"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Product Form -->
                    <FormField
                      :name="`product_items.${index}.product_form`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Form</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter form"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Product Strength -->
                    <FormField
                      :name="`product_items.${index}.product_strength`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Strength</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter strength"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Price -->
                    <FormField
                      :name="`product_items.${index}.price`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Price *</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter price"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Xpedicare URL -->
                    <FormField
                      :name="`product_items.${index}.xpedicare_url`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Xpedicare URL *</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            type="url"
                            placeholder="https://example.com"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  </div>
                </div>

                <div class="flex justify-center">
                  <Button type="button" variant="outline" size="sm" @click="addProgramItem">
                    <Plus class="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>
              </FieldArray>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3">
            <Button variant="outline" type="button" :disabled="isAdding" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isAdding">
              <Loader2 v-if="isAdding" class="mr-2 h-4 w-4 animate-spin" />
              {{ isAdding ? 'Adding...' : 'Add Product' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
