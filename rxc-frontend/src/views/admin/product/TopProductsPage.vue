<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Search, PlusIcon, GripVertical, Package, Loader2 } from 'lucide-vue-next'
import { useTopProductsStore } from '@/stores'
import { useDebounce } from '@vueuse/core'
import ConfirmationDialog from '@/components/ConfirmationDialog.vue'
import { toast } from 'vue-sonner'

const topProductsStore = useTopProductsStore()
const {
  topProducts,
  allProducts,
  isLoading,
  isLoadingAllProducts,
  isAdding,
  isDeleting,
  isReordering,
  error: storeError,
} = storeToRefs(topProductsStore)
const { fetchTopProducts, fetchAllProducts, addTopProduct, deleteTopProduct, reorderTopProducts } =
  topProductsStore

const isAddDialogOpen = ref(false)
const isDeleteDialogOpen = ref(false)
const selectedProductId = ref<string>('')
const searchQuery = ref('')
const categoryFilter = ref('all')
const debouncedSearchQuery = useDebounce(searchQuery, 300)

const filteredAllProducts = computed(() => {
  let filtered = allProducts.value

  if (debouncedSearchQuery.value) {
    const query = debouncedSearchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (product) =>
        product.product_name_title.toLowerCase().includes(query) ||
        product.main_category_name.toLowerCase().includes(query),
    )
  }

  if (categoryFilter.value !== 'all') {
    filtered = filtered.filter((product) => product.main_category_name === categoryFilter.value)
  }

  const topProductIds = new Set(topProducts.value.map((tp) => tp.product_id))
  filtered = filtered.filter((product) => !topProductIds.has(product.id))

  return filtered
})

const availableCategories = computed(() => {
  const categories = new Set(allProducts.value.map((p) => p.main_category_name))
  return Array.from(categories).sort()
})

async function handleAddProduct(productId: string) {
  if (isAdding.value) return

  const result = await addTopProduct(productId)
  if (result) {
    await fetchTopProducts()
    // isAddDialogOpen.value = false
    searchQuery.value = ''
    categoryFilter.value = 'all'
  } else if (storeError.value) {
    toast.error(storeError.value)
  }
}

async function handleDeleteProduct(productId: string) {
  if (isDeleting.value) return

  const result = await deleteTopProduct(productId)
  if (result) {
    await fetchTopProducts()
    isDeleteDialogOpen.value = false
  } else if (storeError.value) {
    toast.error(storeError.value)
  }
}

function openDeleteDialog(productId: string) {
  selectedProductId.value = productId
  isDeleteDialogOpen.value = true
}

async function openAddDialog() {
  isAddDialogOpen.value = true
  if (allProducts.value.length === 0) {
    await fetchAllProducts()
  }
}

const draggingItemId = ref<string | null>(null)

const dragStart = (itemId: string) => {
  draggingItemId.value = itemId
}

const dragOver = (itemId: string, event: DragEvent) => {
  event.preventDefault()

  const draggedItemIndex = topProducts.value.findIndex(
    (item) => item.top_product_id === draggingItemId.value,
  )
  const targetItemIndex = topProducts.value.findIndex((item) => item.top_product_id === itemId)

  if (draggedItemIndex !== -1 && targetItemIndex !== -1 && draggedItemIndex !== targetItemIndex) {
    const draggedItem = topProducts.value.splice(draggedItemIndex, 1)[0]

    topProducts.value.splice(targetItemIndex, 0, draggedItem)
    topProducts.value = topProducts.value.map((d, index) => ({
      ...d,
      sno: index + 1,
    }))
  }
}

const drop = (event: DragEvent) => {
  event.preventDefault()
}

const dragEnd = async () => {
  const itemsIds = topProducts.value.map((item) => item.top_product_id)

  const result = await reorderTopProducts(itemsIds)

  if (result) {
    await fetchTopProducts()
  } else if (storeError.value) {
    toast.error(storeError.value)
  } else {
    toast.error('Failed to reorder top products')
  }
  draggingItemId.value = null
}

onMounted(async () => {
  await fetchTopProducts()
})
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">Top Products</h1>
        <p class="text-muted-foreground text-sm">
          Manage featured products displayed on the homepage
        </p>
      </div>
      <Button v-if="topProducts.length < 6" @click="openAddDialog">
        <PlusIcon class="h-4 w-4" />
        Add Top Product
      </Button>
    </div>

    <!-- Current Top Products -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Package class="h-5 w-5" />
          Current Top Products ({{ topProducts.length }})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="flex items-center justify-center py-8">
          <Loader2 class="h-6 w-6 animate-spin" />
          <span class="ml-2">Loading top products...</span>
        </div>

        <div v-else-if="topProducts.length === 0" class="text-center py-8">
          <Package class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p class="text-muted-foreground">No top products configured</p>
          <p class="text-sm text-muted-foreground">Add products to feature them on the homepage</p>
        </div>

        <div v-else class="space-y-2">
          <!-- Table Header -->
          <div
            class="grid grid-cols-12 gap-4 p-4 bg-muted/50 rounded-t-md border-b font-medium text-sm"
          >
            <div class="col-span-1"></div>
            <div class="col-span-1">#</div>
            <div class="col-span-5">Product</div>
            <div class="col-span-2">Category</div>
            <div class="col-span-2">Type</div>
            <div class="col-span-1">Actions</div>
          </div>

          <!-- Draggable List -->
          <div
            v-for="(product, index) in topProducts"
            :key="product.top_product_id"
            class="space-y-1"
            :draggable="true"
            @dragstart="dragStart(product.top_product_id)"
            @dragover="dragOver(product.top_product_id, $event)"
            @drop="drop"
            @dragend="dragEnd"
          >
            <div
              class="grid grid-cols-12 gap-4 p-4 bg-background border rounded-md hover:bg-muted/50 transition-colors"
              :class="{ 'opacity-50': isReordering }"
            >
              <!-- Position -->
              <div class="col-span-1 flex items-center">
                <!-- Drag Handle -->
                <div
                  class="drag-handle cursor-grab active:cursor-grabbing p-1 rounded hover:bg-muted"
                  :class="{ 'cursor-not-allowed': isReordering }"
                >
                  <GripVertical class="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
              <div class="col-span-1 flex items-center">
                <span class="font-medium">{{ index + 1 }}</span>
              </div>

              <!-- Product Info -->
              <div class="col-span-5 flex items-center gap-3">
                <div class="w-12 h-12 rounded-md overflow-hidden bg-muted">
                  <img
                    v-if="product.image"
                    :src="product.image"
                    :alt="product.product_name_title"
                    class="w-full h-full object-cover"
                  />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <Package class="h-6 w-6 text-muted-foreground" />
                  </div>
                </div>
                <div>
                  <div class="font-medium">{{ product.product_name_title }}</div>
                  <div v-if="product.product_title" class="text-sm text-muted-foreground">
                    {{ product.product_title }}
                  </div>
                </div>
              </div>

              <!-- Category -->
              <div class="col-span-2 flex items-center">
                <Badge variant="secondary">{{ product.main_category_name }}</Badge>
              </div>

              <!-- Type -->
              <div class="col-span-2 flex items-center">
                <Badge :variant="product.product_type === 'single' ? 'default' : 'outline'">
                  {{ product.product_type }}
                </Badge>
              </div>

              <!-- Actions -->
              <div class="col-span-1 flex items-center gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  :disabled="isDeleting"
                  @click="openDeleteDialog(product.top_product_id)"
                >
                  Remove
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Add Product Dialog -->
    <Dialog v-model:open="isAddDialogOpen">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Add Top Product</DialogTitle>
          <DialogDescription> Select a product to add to the top products list </DialogDescription>
        </DialogHeader>

        <div class="flex-1 overflow-hidden flex flex-col">
          <!-- Filters -->
          <div class="flex gap-4 mb-4">
            <div class="flex-1">
              <div class="relative">
                <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input v-model="searchQuery" placeholder="Search products..." class="pl-8" />
              </div>
            </div>
            <Select v-model="categoryFilter">
              <SelectTrigger class="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem
                  v-for="category in availableCategories"
                  :key="category"
                  :value="category"
                >
                  {{ category }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Products List -->
          <div class="flex-1 overflow-auto border rounded-md">
            <div v-if="isLoadingAllProducts" class="flex items-center justify-center py-8">
              <Loader2 class="h-6 w-6 animate-spin" />
              <span class="ml-2">Loading products...</span>
            </div>

            <div v-else-if="filteredAllProducts.length === 0" class="text-center py-8">
              <Package class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p class="text-muted-foreground">No products found</p>
            </div>

            <Table v-else>
              <TableHeader class="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead class="w-24">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="product in filteredAllProducts" :key="product.id">
                  <TableCell class="font-medium">{{ product.product_name_title }}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{{ product.main_category_name }}</Badge>
                  </TableCell>
                  <TableCell>
                    <Button size="sm" :disabled="isAdding" @click="handleAddProduct(product.id)">
                      <PlusIcon class="h-4 w-4" /> Add
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <ConfirmationDialog
      v-model:open="isDeleteDialogOpen"
      title="Remove Top Product"
      description="Are you sure you want to remove this product from the top products list?"
      :loading="isDeleting"
      @confirm="handleDeleteProduct(selectedProductId)"
    />
  </div>
</template>
