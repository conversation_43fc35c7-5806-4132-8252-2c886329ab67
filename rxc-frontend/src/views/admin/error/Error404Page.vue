<template>
  <div class="flex flex-col items-center justify-center min-h-screen px-4 py-16">
    <div class="text-center">
      <h1 class="text-5xl sm:text-8xl font-bold tracking-tight">404</h1>
      <p class="mt-2 text-xl text-muted-foreground">Page not found</p>
      <router-link
        :to="{ name: 'admin-dashboard' }"
        class="mt-6 inline-flex items-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90"
      >
        Go back home
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Error404Page',
})
</script>
