<!-- SubCategory Form Drawer component -->
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { storeToRefs } from 'pinia'
import { useSubCategoryStore, type SubCategoryPayload } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet'
import { Loader2 } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { isEmptyObject, scrollToTop } from '@/lib'

const props = defineProps<{
  open: boolean
  subCategoryId?: string
}>()

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
  (e: 'success'): void
}>()

const subCategoryStore = useSubCategoryStore()
const {
  selectedSubCategory,
  isAdding,
  isUpdating,
  isLoadingDetails,
  error: storeError,
  fieldErrors,
  categoryOptions,
  isLoadingCategoryOptions,
} = storeToRefs(subCategoryStore)
const { addSubCategory, updateSubCategory, getSubCategoryById, fetchCategoryOptions } =
  subCategoryStore

const formError = ref('')

const formSchema = toTypedSchema(
  z.object({
    main_category_id: z.string().min(1, 'Main category is required'),
    name: z.string().min(1, 'Sub-category name is required'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
  initialValues: {
    main_category_id: '',
    name: '',
  },
})

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isAdding.value || isUpdating.value) return
  formError.value = ''

  const formValues = values as SubCategoryPayload
  let result

  if (props.subCategoryId) {
    result = await updateSubCategory({ ...formValues, id: props.subCategoryId })
  } else {
    result = await addSubCategory(formValues)
  }

  if (result) {
    emit('success')
    emit('update:open', false)
  } else if (!isEmptyObject(fieldErrors.value)) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof SubCategoryPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = `Failed to ${props.subCategoryId ? 'update' : 'add'} sub-category. Please try again.`
  }

  scrollToTop()
})

async function fetchSubCategoryDetails() {
  if (!props.subCategoryId || isLoadingDetails.value) return

  const result = await getSubCategoryById(props.subCategoryId)

  if (result) {
    form.setValues({
      main_category_id: selectedSubCategory.value?.main_category_id || '',
      name: selectedSubCategory.value?.name || '',
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  }
}

watch(
  () => props.open,
  async (newValue) => {
    if (newValue) {
      await fetchCategoryOptions()
      if (props.subCategoryId) {
        await fetchSubCategoryDetails()
      } else {
        // Reset form for new sub-category
        form.setValues({
          main_category_id: '',
          name: '',
        })
      }
    }
    // Always reset error when drawer opens/closes
    formError.value = ''
  },
)

onMounted(async () => {
  if (props.open) {
    await fetchCategoryOptions()
    if (props.subCategoryId) {
      await fetchSubCategoryDetails()
    }
  }
})
</script>

<template>
  <Sheet :open="open" @update:open="$emit('update:open', $event)">
    <SheetContent class="sm:max-w-[425px]">
      <SheetHeader>
        <SheetTitle>{{ subCategoryId ? 'Edit Sub-Category' : 'Add New Sub-Category' }}</SheetTitle>
        <SheetDescription>
          {{ subCategoryId ? 'Update sub-category details' : 'Create a new sub-category' }}
        </SheetDescription>
      </SheetHeader>

      <!-- Loading State -->
      <div v-if="subCategoryId && isLoadingDetails" class="flex items-center justify-center py-8">
        <Loader2 class="h-8 w-8 animate-spin" />
      </div>

      <!-- Form -->
      <form v-else class="space-y-6 px-4" @submit.prevent="onSubmit">
        <Alert v-if="formError" variant="destructive">
          <AlertDescription>{{ formError }}</AlertDescription>
        </Alert>

        <div class="space-y-6">
          <!-- Main Category -->
          <FormField name="main_category_id" v-slot="{ field, errorMessage }">
            <FormItem>
              <FormLabel>Main Category</FormLabel>
              <FormControl>
                <Select
                  :model-value="field.value"
                  @update:model-value="field.onChange($event)"
                  :disabled="isLoadingCategoryOptions"
                >
                  <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                    <SelectValue placeholder="Select a main category" />
                  </SelectTrigger>
                  <SelectContent>
                    <div v-if="isLoadingCategoryOptions" class="p-2 text-center text-sm">
                      Loading main categories...
                    </div>
                    <div v-else-if="categoryOptions.length === 0" class="p-2 text-center text-sm">
                      No main categories available
                    </div>
                    <SelectItem
                      v-for="mainCategory in categoryOptions"
                      :key="mainCategory.id"
                      :value="mainCategory.id"
                    >
                      {{ mainCategory.name
                      }}{{ mainCategory.gender ? ` (${mainCategory.gender})` : '' }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Sub-Category Name -->
          <FormField name="name" v-slot="{ componentField, errorMessage }">
            <FormItem>
              <FormLabel>Sub-Category Name</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  placeholder="Enter sub-category name"
                  :aria-invalid="!!errorMessage"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        </div>

        <div class="flex justify-center gap-3 pt-4">
          <Button
            class="flex-1"
            variant="outline"
            type="button"
            :disabled="isAdding || isUpdating"
            @click="$emit('update:open', false)"
          >
            Cancel
          </Button>
          <Button type="submit" class="flex-1" :disabled="isAdding || isUpdating">
            <Loader2 v-if="isAdding || isUpdating" class="mr-2 h-4 w-4 animate-spin" />
            {{
              subCategoryId
                ? isUpdating
                  ? 'Updating...'
                  : 'Update Sub-Category'
                : isAdding
                  ? 'Adding...'
                  : 'Add Sub-Category'
            }}
          </Button>
        </div>
      </form>
    </SheetContent>
  </Sheet>
</template>
