<!-- Category Form Drawer component -->
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { storeToRefs } from 'pinia'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet'
import { Image, Loader2, X } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { useCategoryStore, type CategoryPayload } from '@/stores'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { isEmptyObject, scrollToTop } from '@/lib'
import Textarea from '@/components/ui/textarea/Textarea.vue'

const props = defineProps<{
  open: boolean
  categoryId?: string
}>()

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
  (e: 'success'): void
}>()

const categoryStore = useCategoryStore()
const {
  isAdding,
  isUpdating,
  isUpdatingImg,
  isLoadingDetails,
  selectedCategory,
  error: storeError,
  fieldErrors,
} = storeToRefs(categoryStore)
const { addCategory, updateCategory, updateCategoryImg, getCategoryById } = categoryStore

const formError = ref('')
const fileInputRef = ref<HTMLInputElement | null>(null)

const formSchema = toTypedSchema(
  z.object({
    name: z.string().min(1, 'Category name is required'),
    description: z.string().max(100, 'Description must be at most 100 characters long').optional(),
    image: z.string().min(1, 'Image is required'),
    gender: z.string().optional(),
  }),
)

const form = useForm({
  validationSchema: formSchema,
  initialValues: {
    name: '',
    description: '',
    image: '',
    gender: '',
  },
})

function triggerImagePicker() {
  fileInputRef.value?.click()
}

function handleImageChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return
  const reader = new FileReader()
  reader.onload = async () => {
    const base64 = reader.result as string
    form.setFieldValue('image', base64)

    if (props.categoryId) {
      const result = await updateCategoryImg({ id: props.categoryId, image: base64 })
      if (!result && storeError.value) {
        formError.value = storeError.value
      }
    }

    // Re-validate the field
    form.validateField('image')
    // Reset input
    if (fileInputRef.value) fileInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isAdding.value || isUpdating.value) return
  formError.value = ''

  const formValues = values as CategoryPayload
  let result

  if (props.categoryId) {
    result = await updateCategory({
      id: props.categoryId,
      name: formValues.name,
      description: formValues.description,
      gender: formValues.gender,
    })
  } else {
    result = await addCategory(formValues)
  }

  if (result) {
    emit('success')
    emit('update:open', false)
  } else if (!isEmptyObject(fieldErrors.value)) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof CategoryPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = `Failed to ${props.categoryId ? 'update' : 'add'} category. Please try again.`
  }

  scrollToTop()
})

async function fetchCategoryDetails() {
  if (!props.categoryId || isLoadingDetails.value) return

  const result = await getCategoryById(props.categoryId)

  if (result) {
    form.setValues({
      name: selectedCategory.value?.name || '',
      description: selectedCategory.value?.description || '',
      gender: selectedCategory.value?.gender || '',
      image: selectedCategory.value?.image || '',
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  }
}

watch(
  () => props.open,
  async (newValue) => {
    if (newValue && props.categoryId) {
      await fetchCategoryDetails()
    } else if (newValue) {
      form.setValues({
        name: '',
        description: '',
        image: '',
        gender: '',
      })
    }

    // reset error when drawer opens/closes
    formError.value = ''
  },
)

onMounted(() => {
  if (props.open && props.categoryId) {
    fetchCategoryDetails()
  }
})
</script>

<template>
  <Sheet :open="open" @update:open="$emit('update:open', $event)">
    <SheetContent class="sm:max-w-[425px]">
      <SheetHeader>
        <SheetTitle>{{ categoryId ? 'Edit Category' : 'Add New Category' }}</SheetTitle>
        <SheetDescription>
          {{ categoryId ? 'Update category details' : 'Create a new category' }}
        </SheetDescription>
      </SheetHeader>

      <!-- Loading State -->
      <div v-if="categoryId && isLoadingDetails" class="flex items-center justify-center py-8">
        <Loader2 class="h-8 w-8 animate-spin" />
      </div>

      <!-- Form -->
      <form v-else class="space-y-6 px-4" @submit.prevent="onSubmit">
        <Alert v-if="formError" variant="destructive">
          <AlertDescription>{{ formError }}</AlertDescription>
        </Alert>

        <div class="space-y-6">
          <!-- Category Name -->
          <FormField name="name" v-slot="{ componentField, errorMessage }">
            <FormItem>
              <FormLabel>Category Name</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  placeholder="Enter category name"
                  :aria-invalid="!!errorMessage"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Category Description -->
          <FormField name="description" v-slot="{ componentField, errorMessage }">
            <FormItem>
              <FormLabel>Category Description (optional)</FormLabel>
              <FormControl>
                <Textarea
                  v-bind="componentField"
                  placeholder="Enter category description"
                  :aria-invalid="!!errorMessage"
                  maxlength="100"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Gender -->
          <FormField name="gender" v-slot="{ field, errorMessage }">
            <FormItem>
              <FormLabel>Limit to Gender (optional)</FormLabel>
              <FormControl>
                <div class="relative w-full">
                  <Select :model-value="field.value" @update:model-value="field.onChange($event)">
                    <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                      <SelectValue placeholder="Select a gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="item in ['Male', 'Female']" :key="item" :value="item">
                        {{ item }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    v-if="field.value"
                    type="button"
                    variant="ghost"
                    class="absolute right-8 top-1/2 -translate-y-1/2 h-4 w-4 p-0 hover:bg-transparent"
                    @click="field.onChange('')"
                  >
                    <X class="h-3 w-3" />
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Category Image -->
          <FormField name="image">
            <FormItem>
              <FormLabel>Category Image</FormLabel>
              <div class="flex items-center gap-4">
                <img
                  v-if="form.values.image"
                  :src="form.values.image"
                  alt="Selected image preview"
                  class="h-16 w-16 rounded object-cover border"
                />
                <div v-else class="h-16 w-16 rounded border flex items-center justify-center">
                  <Image class="h-8 w-8" />
                </div>
                <Button
                  type="button"
                  :disabled="isAdding || isUpdatingImg"
                  @click="triggerImagePicker"
                >
                  <Loader2 v-if="isUpdatingImg" class="mr-2 h-4 w-4 animate-spin" />
                  {{ isUpdatingImg ? 'Updating...' : 'Select Image' }}
                </Button>
                <input
                  ref="fileInputRef"
                  type="file"
                  accept="image/*"
                  class="hidden"
                  @change="handleImageChange"
                />
              </div>
              <FormMessage />
            </FormItem>
          </FormField>
        </div>

        <div class="flex justify-center gap-3 pt-4">
          <Button
            class="flex-1"
            variant="outline"
            type="button"
            :disabled="isAdding || isUpdating"
            @click="$emit('update:open', false)"
          >
            Cancel
          </Button>
          <Button class="flex-1" type="submit" :disabled="isAdding || isUpdating">
            <Loader2 v-if="isAdding || isUpdating" class="mr-2 h-4 w-4 animate-spin" />
            {{
              categoryId
                ? isUpdating
                  ? 'Updating...'
                  : 'Update Category'
                : isAdding
                  ? 'Adding...'
                  : 'Add Category'
            }}
          </Button>
        </div>
      </form>
    </SheetContent>
  </Sheet>
</template>
