<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { toast } from 'vue-sonner'
import { Lock, Eye, EyeOff } from 'lucide-vue-next'
import { apiClient } from '@/composables'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import { Alert, AlertDescription } from '@/components/ui/alert'

type ChangePasswordPayload = {
  current_password: string
  new_password: string
  password_confirmation: string
}

const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const isChangingPassword = ref(false)
const formError = ref('')

const changePasswordSchema = toTypedSchema(
  z
    .object({
      current_password: z.string().min(1, 'Current password is required'),
      new_password: z.string().min(8, 'Password must be at least 8 characters'),
      password_confirmation: z.string().min(1, 'Please confirm your password'),
    })
    .refine((data) => data.new_password === data.password_confirmation, {
      message: "Passwords don't match",
      path: ['password_confirmation'],
    }),
)

const changePasswordForm = useForm({
  validationSchema: changePasswordSchema,
  initialValues: {
    current_password: '',
    new_password: '',
    password_confirmation: '',
  },
})

const handleChangePasswordSubmit = changePasswordForm.handleSubmit(async (values) => {
  if (isChangingPassword.value) return
  isChangingPassword.value = true
  formError.value = ''

  try {
    const formValues = values as ChangePasswordPayload
    const response = await apiClient.post<ApiResponse>('/admin/change-password', formValues)
    if (response.data.status === 200) {
      toast.success(response.data.message || 'Password changed successfully')
      changePasswordForm.resetForm()
    } else if (response.data.errors) {
      Object.entries(response.data.errors).forEach(([key, value]) => {
        changePasswordForm.setFieldError(key as keyof ChangePasswordPayload, value[0])
      })
    } else {
      formError.value = response.data.message || 'Failed to change password. Please try again.'
    }
  } catch (error: any) {
    console.error('Password change error:', error)
    formError.value = processErrors(error, 'Failed to change password. Please try again.')
  } finally {
    isChangingPassword.value = false
  }
})
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Lock class="h-5 w-5" />
        Change Password
      </CardTitle>
      <CardDescription> Update your password to keep your account secure. </CardDescription>
    </CardHeader>
    <CardContent>
      <form @submit="handleChangePasswordSubmit">
        <div class="space-y-4">
          <FormField v-slot="{ componentField }" name="current_password">
            <FormItem>
              <FormLabel>Current Password</FormLabel>
              <FormControl>
                <div class="relative">
                  <Input
                    placeholder="Enter your current password"
                    v-bind="componentField"
                    :type="showCurrentPassword ? 'text' : 'password'"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    @click="showCurrentPassword = !showCurrentPassword"
                    tabindex="-1"
                  >
                    <Eye v-if="showCurrentPassword" class="h-4 w-4" />
                    <EyeOff v-else class="h-4 w-4" />
                    <span class="sr-only"
                      >{{ showCurrentPassword ? 'Hide' : 'Show' }} password</span
                    >
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="new_password">
            <FormItem>
              <FormLabel>New Password</FormLabel>
              <FormControl>
                <div class="relative">
                  <Input
                    placeholder="Enter your new password"
                    v-bind="componentField"
                    :type="showNewPassword ? 'text' : 'password'"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    @click="showNewPassword = !showNewPassword"
                    tabindex="-1"
                  >
                    <Eye v-if="showNewPassword" class="h-4 w-4" />
                    <EyeOff v-else class="h-4 w-4" />
                    <span class="sr-only">{{ showNewPassword ? 'Hide' : 'Show' }} password</span>
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="password_confirmation">
            <FormItem>
              <FormLabel>Confirm New Password</FormLabel>
              <FormControl>
                <div class="relative">
                  <Input
                    placeholder="Confirm your new password"
                    v-bind="componentField"
                    :type="showConfirmPassword ? 'text' : 'password'"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    @click="showConfirmPassword = !showConfirmPassword"
                    tabindex="-1"
                  >
                    <Eye v-if="showConfirmPassword" class="h-4 w-4" />
                    <EyeOff v-else class="h-4 w-4" />
                    <span class="sr-only"
                      >{{ showConfirmPassword ? 'Hide' : 'Show' }} password</span
                    >
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <Button type="submit" :disabled="isChangingPassword" class="w-full mt-4">
            <Lock class="mr-2 h-4 w-4" />
            {{ isChangingPassword ? 'Changing Password...' : 'Change Password' }}
          </Button>
        </div>
      </form>
    </CardContent>
  </Card>
</template>
