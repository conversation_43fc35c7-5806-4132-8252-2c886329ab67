<script setup lang="ts">
import { capitalize, formatCurrency } from '@/lib'
import Header from '@/components/user/Header.vue'
import Footer from '@/components/user/Footer.vue'
import ProductItem from '@/components/user/ProductItem.vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { IconArrowRight, IconMoodSad } from '@tabler/icons-vue'
import { useGlobalSettingsStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { usePublicProductStore } from '@/stores/publicProductStore'
import { toast } from 'vue-sonner'
import ProgramItemsModal from '@/components/user/ProgramItemsModal.vue'

const route = useRoute()
const globalStore = useGlobalSettingsStore()
const { content } = storeToRefs(globalStore)

const productStore = usePublicProductStore()
const { productDetails, isLoadingDetails, detailsError } = storeToRefs(productStore)
const { fetchProductDetails } = usePublicProductStore()

const isModalOpen = ref(false)

const relatedProducts = computed(() => {
  if (!productDetails.value) return [] as any[]
  return productDetails.value.recommendProducts
})

// const heroMeta = computed(() => {
//   const slug = (productDetails.value as any)?.category_slug as string | undefined
//   const gradients: Record<string, string> = {
//     'weight-loss': 'from-amber-50 via-orange-50 to-amber-100',
//     'sexual-health': 'from-cyan-50 via-cyan-50 to-blue-100',
//     'hair-health': 'from-emerald-50 via-emerald-50 to-teal-100',
//   }
//   return { gradient: gradients[slug || ''] || 'from-neutral-50 via-neutral-50 to-slate-100' }
// })

const productFAQs = ref<{ question: string; answer: string; open: boolean }[]>([])

watch(productDetails, (pd) => {
  if (pd && pd.product_faqs) {
    productFAQs.value = pd.product_faqs.map((faq: any) => ({ ...faq, open: false }))
  }
})

const toggle = (index: number) => {
  productFAQs.value = productFAQs.value.map((faq, i) => {
    if (i === index) {
      faq.open = !faq.open
    } else {
      faq.open = false
    }
    return faq
  })
}

onMounted(async () => {
  await fetchProductDetails(route.params.slug as string)
  if (detailsError.value) {
    toast.error(detailsError.value)
  }
})
</script>

<template>
  <div class="bg-neutral-50">
    <Header />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="isLoadingDetails" class="bg-neutral-50 rounded-2xl shadow-sm p-8">
        <div class="animate-pulse">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="w-full md:w-1/2 lg:w-2/5">
              <div class="bg-gray-200 rounded-xl aspect-square"></div>
            </div>
            <div class="w-full md:w-1/2 lg:w-3/5">
              <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div class="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div class="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div class="h-10 bg-gray-200 rounded w-full mb-6"></div>
              <div class="h-12 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <!-- <div v-else-if="detailsError" class="bg-neutral-50 rounded-2xl shadow-sm p-8">
        <div class="text-center text-red-500">
          <IconMoodSad class="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3>Oops! Something went wrong.</h3>
          <p>{{ detailsError }}</p>
        </div>
      </div> -->

      <!-- Product Details -->
      <div v-else-if="productDetails">
        <!-- Hero / Top Overview: full-bleed, no outer card -->
        <div
          :class="[
            'rounded-2xl p-6 sm:p-8 bg-gradient-to-br border border-cyan-100',
            // heroMeta.gradient,
            'from-cyan-50 via-cyan-50 to-blue-100',
          ]"
        >
          <!-- Breadcrumbs -->
          <nav class="text-sm text-neutral-700 mb-4 flex items-center gap-1">
            <RouterLink :to="{ name: 'Home' }" class="hover:text-neutral-900">Home</RouterLink>
            <span>/</span>
            <RouterLink
              :to="{
                name: 'Treatments',
                params: { category_slug: productDetails.main_category_slug },
              }"
              class="hover:text-neutral-900"
              >{{ productDetails.main_category_name }}</RouterLink
            >
            <span>/</span>
            <span class="text-neutral-900 font-medium">{{
              productDetails.product_name_title
            }}</span>
          </nav>

          <div class="flex flex-col md:flex-row items-center gap-8">
            <!-- Product Image -->
            <div class="w-full md:w-1/2 lg:w-2/5 flex justify-center md:justify-start">
              <img
                :src="productDetails.image"
                :alt="productDetails.product_name_title"
                class="max-w-full max-h-[460px] object-contain"
              />
            </div>

            <!-- Product Info -->
            <div class="w-full md:w-1/2 lg:w-3/5">
              <h1 class="text-3xl sm:text-5xl font-bold text-neutral-900 mb-3">
                {{ productDetails.product_name_title }}
              </h1>

              <div class="flex items-center flex-wrap gap-2 mb-4">
                <template v-if="productDetails.product_type === 'single'">
                  <span
                    v-if="productDetails.product_form"
                    class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                    >{{ capitalize(productDetails.product_form) }}</span
                  >
                  <span
                    v-if="productDetails.product_strength"
                    class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                    >{{ productDetails.product_strength }}
                  </span>
                  <!-- <span
                    v-if="productDetails.product_qty"
                    class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                    >{{ productDetails.product_qty }}
                  </span> -->
                </template>
              </div>

              <div class="flex items-baseline gap-2 mb-6">
                <span class="text-lg font-semibold text-neutral-900">
                  Starting at {{ formatCurrency(productDetails.price) }}
                </span>
              </div>

              <div class="flex flex-col sm:flex-row gap-3">
                <template v-if="productDetails.product_type === 'single'">
                  <a
                    :href="productDetails.xpedicare_url"
                    class="w-full inline-flex justify-center items-center gap-2 bg-black text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-300 cursor-pointer text-center"
                  >
                    Continue Online Visit
                    <IconArrowRight class="w-5 h-5 me-2" stroke-width="2" />
                  </a>
                </template>
                <template v-if="productDetails.product_type === 'programs'">
                  <button
                    class="w-full inline-flex justify-center items-center gap-2 bg-black text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-300 cursor-pointer text-center"
                    @click="isModalOpen = true"
                  >
                    Continue Online Visit
                    <IconArrowRight class="w-5 h-5 me-2" stroke-width="2" />
                  </button>
                </template>
              </div>
            </div>
          </div>
        </div>

        <div class="py-12 space-y-12">
          <!-- Product Descriptions -->
          <div
            v-for="description in productDetails.product_descriptions"
            :key="description.title"
            class="bg-white rounded-3xl p-8"
          >
            <div class="flex items-center gap-3 mb-6">
              <h2 class="text-2xl md:text-3xl font-bold text-neutral-900">
                {{ description.title }}
              </h2>
            </div>
            <div
              class="text-neutral-700 prose prose-lg max-w-none leading-relaxed"
              v-html="description.description"
            ></div>
          </div>

          <!-- Benefits -->
          <!-- <div
            v-if="productDetails.benefits"
            class="bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 rounded-3xl p-8"
          >
            <div class="flex items-center gap-3 mb-6">
              <h2 class="text-2xl md:text-3xl font-bold text-green-900">Key Benefits</h2>
            </div>
            <div
              class="prose prose-lg text-green-800 max-w-none leading-relaxed"
              v-html="productDetails.benefits"
            ></div>
          </div> -->

          <!-- Safety & Side Effects -->
          <!-- <div
            v-if="productDetails.safety_and_side_effects"
            class="bg-gradient-to-br from-amber-50 via-orange-50 to-amber-100 rounded-3xl p-8"
          >
            <div class="flex items-center gap-3 mb-6">
              <h2 class="text-2xl md:text-3xl font-bold text-amber-900">Safety & Side Effects</h2>
            </div>
            <div
              class="prose prose-lg text-amber-800 max-w-none leading-relaxed"
              v-html="productDetails.safety_and_side_effects"
            ></div>
          </div> -->

          <!-- FAQs -->
          <div v-if="productFAQs.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-12 py-12">
            <div class="lg:sticky lg:top-32">
              <h2 class="text-4xl lg:text-5xl font-medium text-gray-900 mb-6">
                Frequently asked questions
              </h2>
              <p class="text-lg text-gray-600 mb-8">
                Find answers to common questions about our medication subscriptions and medical
                review process.
              </p>
            </div>
            <div class="space-y-4">
              <div
                v-for="(faq, idx) in productFAQs"
                :key="idx"
                class="border border-gray-200 rounded-lg"
              >
                <button
                  @click="toggle(idx)"
                  class="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 transition-colors"
                >
                  <span class="font-semibold text-gray-900">
                    {{ faq.question }}
                  </span>
                  <svg
                    class="w-6 h-6 text-gray-500 transition-transform duration-200"
                    :class="{ 'rotate-45': faq.open }"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    ></path>
                  </svg>
                </button>
                <div
                  class="overflow-hidden transition-all duration-300 ease-in-out"
                  :class="{ 'max-h-96': faq.open, 'max-h-0': !faq.open }"
                >
                  <div class="px-6 pb-6 text-gray-600">
                    <p>{{ faq.answer }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Related Treatments -->
          <div v-if="relatedProducts.length">
            <h2 class="text-2xl md:text-4xl font-semibold text-neutral-900 text-center mb-8">
              Related Treatments
            </h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductItem v-for="p in relatedProducts" :key="p.slug" :product="p" />
            </div>
          </div>
        </div>
      </div>

      <!-- Product Not Found -->
      <div v-else class="bg-neutral-50 rounded-2xl shadow-sm p-12 text-center">
        <div class="max-w-md mx-auto">
          <IconMoodSad class="w-16 h-16 text-gray-400 mx-auto mb-4" stroke-width="2" />
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Treatment not found</h3>
          <p class="text-gray-600 mb-6">
            The treatment you are looking for does not exist or has been removed.
          </p>
          <RouterLink
            :to="{ name: 'Treatments' }"
            class="bg-black text-white px-6 py-3 rounded-full hover:bg-gray-800 transition-all duration-300 cursor-pointer"
          >
            Browse Treatments
          </RouterLink>
        </div>
      </div>
    </div>

    <Footer :content="content.footer" :is-admin="false" />
    <ProgramItemsModal
      v-if="productDetails && productDetails.product_type === 'programs'"
      :open="isModalOpen"
      :product-name="productDetails.product_name_title"
      :items="productDetails.product_items"
      @update:open="isModalOpen = $event"
    />
  </div>
</template>
