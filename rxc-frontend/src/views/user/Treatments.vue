<script setup lang="ts">
import Header from '@/components/user/Header.vue'
import { computed, onMounted, watch } from 'vue'
import ProductItem from '@/components/user/ProductItem.vue'
import { useRoute } from 'vue-router'
import { IconChevronRight, IconMoodSad } from '@tabler/icons-vue'
import { usePublicProductStore } from '@/stores/publicProductStore'
import { storeToRefs } from 'pinia'

const productStore = usePublicProductStore()
const { products, pagination, isLoadingList, categories, isLoadingCategories } =
  storeToRefs(productStore)

const route = useRoute()
const activeCategorySlug = computed(() => (route.params as any).category_slug || null)

watch(activeCategorySlug, async (newSlug) => {
  productStore.slug = newSlug || undefined
  await productStore.fetchProducts()
})

onMounted(async () => {
  await Promise.all([productStore.fetchAllCategories(), productStore.fetchProducts()])
})

const categoryMeta: Record<string, { title: string; blurb: string; gradient: string }> = {
  'weight-loss': {
    title: 'Weight Loss',
    blurb: 'Clinically-proven GLP-1 options to help you lose weight and keep it off.',
    gradient: 'from-amber-50 via-orange-50 to-orange-100',
  },
  'sexual-health': {
    title: 'Sexual Health',
    blurb: 'Proven treatments to boost performance and confidence.',
    gradient: 'from-cyan-50 via-cyan-50 to-blue-100',
  },
  'hair-health': {
    title: 'Hair Health',
    blurb: 'Evidence-based solutions to slow loss and encourage regrowth.',
    gradient: 'from-emerald-50 via-emerald-50 to-teal-100',
  },
}

const pageMeta = computed(() => {
  if (activeCategorySlug.value && categoryMeta[activeCategorySlug.value as string]) {
    return categoryMeta[activeCategorySlug.value as string]
  }
  return {
    title: 'All Treatments',
    blurb: 'Explore our most trusted, high-quality treatments across categories.',
    gradient: 'from-neutral-50 via-neutral-50 to-slate-100',
  } as const
})

const resultsCount = computed(() => products.value.length)

async function nextPage() {
  if (pagination.value.currentPage < pagination.value.totalPages) {
    pagination.value.currentPage++
    await productStore.fetchProducts()
  }
}

async function prevPage() {
  if (pagination.value.currentPage > 1) {
    pagination.value.currentPage--
    await productStore.fetchProducts()
  }
}
</script>

<template>
  <div class="bg-neutral-50 min-h-screen">
    <Header />

    <!-- Page Intro / Breadcrumbs -->
    <section class="pt-8 sm:pt-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="text-sm text-neutral-600 mb-4 flex items-center gap-1">
          <RouterLink :to="{ name: 'Home' }" class="hover:text-neutral-900">Home</RouterLink>
          <IconChevronRight class="w-4 h-4" />
          <RouterLink :to="{ name: 'Treatments' }" class="hover:text-neutral-900"
            >Treatments</RouterLink
          >
          <template v-if="activeCategorySlug">
            <IconChevronRight class="w-4 h-4" />
            <span class="text-neutral-900 font-medium">
              {{ categories.find((c) => c.slug === activeCategorySlug)?.name || pageMeta.title }}
            </span>
          </template>
        </nav>

        <!-- Hero header area -->
        <div
          :class="[
            'rounded-2xl p-6 sm:p-8 border',
            `bg-gradient-to-br ${pageMeta.gradient}`,
            'border-neutral-200',
          ]"
        >
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 class="text-3xl sm:text-4xl font-bold text-neutral-900 tracking-tight">
                {{ activeCategorySlug ? `${pageMeta.title} Treatments` : pageMeta.title }}
              </h1>
              <p class="text-neutral-700 mt-2 max-w-2xl">{{ pageMeta.blurb }}</p>
            </div>
            <div class="flex items-center gap-2">
              <span
                class="text-sm text-neutral-700 bg-white border border-neutral-200 rounded-full px-3 py-1"
              >
                {{ resultsCount }} result{{ resultsCount === 1 ? '' : 's' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content with sidebar -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
      <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <!-- Sidebar -->
        <div class="lg:col-span-3">
          <div class="bg-white rounded-2xl p-6 border border-neutral-200 shadow-sm">
            <h3 class="text-xl font-bold mb-6 text-neutral-900 flex items-center gap-2">
              <span
                class="inline-block w-2 h-6 bg-gradient-to-b from-amber-400 to-orange-400 rounded-full mr-2"
              ></span>
              Categories
            </h3>
            <div v-if="isLoadingCategories" class="flex flex-col gap-3">
              <div v-for="i in 4" :key="i" class="animate-pulse">
                <div class="h-10 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
            <div v-else class="flex flex-col gap-2">
              <RouterLink
                :to="{ name: 'Treatments' }"
                class="flex items-center gap-3 px-4 py-2 rounded-lg text-base font-medium border transition-all duration-150 shadow-sm"
                :class="
                  !activeCategorySlug
                    ? 'bg-gradient-to-r from-black to-neutral-800 text-white border-black shadow-lg scale-105'
                    : 'bg-white text-neutral-800 border-neutral-300 hover:border-orange-400 hover:bg-orange-50'
                "
              >
                <span class="inline-block w-2 h-2 rounded-full bg-neutral-400"></span>
                All
              </RouterLink>
              <RouterLink
                v-for="cat in categories"
                :key="cat.slug"
                :to="{ name: 'Treatments', params: { category_slug: cat.slug } }"
                class="flex items-center gap-3 px-4 py-2 rounded-lg text-base font-medium border transition-all duration-150 shadow-sm"
                :class="
                  activeCategorySlug === cat.slug
                    ? 'bg-gradient-to-r from-orange-400 to-amber-300 text-white border-orange-500 shadow-lg scale-105'
                    : 'bg-white text-neutral-800 border-neutral-300 hover:border-orange-400 hover:bg-orange-50'
                "
              >
                <span
                  class="inline-block w-2 h-2 rounded-full"
                  :class="activeCategorySlug === cat.slug ? 'bg-orange-500' : 'bg-neutral-400'"
                ></span>
                {{ cat.name }}
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-9 mt-6 lg:mt-0">
          <!-- Results Grid / Empty state -->
          <div v-if="isLoadingList" class="text-center py-12">
            <div class="animate-pulse">Loading...</div>
          </div>
          <div
            v-else-if="resultsCount === 0"
            class="bg-white border border-neutral-200 rounded-2xl p-10 text-center"
          >
            <div class="flex flex-col items-center justify-center gap-3">
              <IconMoodSad class="w-10 h-10 text-neutral-500" />
              <h3 class="text-lg font-semibold text-neutral-900">No treatments found</h3>
              <p class="text-neutral-600 max-w-md">Try exploring another category.</p>
            </div>
          </div>
          <div v-else>
            <!-- Products Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductItem v-for="product in products" :key="product.slug" :product="product" />
            </div>

            <!-- Pagination -->
            <div class="mt-10 flex flex-col items-center gap-3">
              <div class="flex gap-2">
                <button
                  @click="prevPage"
                  :disabled="pagination.currentPage <= 1"
                  class="px-5 py-2 rounded-full text-base font-semibold border transition-all duration-150 shadow-sm"
                  :class="
                    pagination.currentPage <= 1
                      ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                      : 'bg-white text-neutral-900 border-orange-400 hover:bg-orange-50 hover:border-orange-500'
                  "
                >
                  &larr; Previous
                </button>
                <button
                  @click="nextPage"
                  :disabled="pagination.currentPage >= pagination.totalPages"
                  class="px-5 py-2 rounded-full text-base font-semibold border transition-all duration-150 shadow-sm"
                  :class="
                    pagination.currentPage >= pagination.totalPages
                      ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                      : 'bg-white text-neutral-900 border-orange-400 hover:bg-orange-50 hover:border-orange-500'
                  "
                >
                  Next &rarr;
                </button>
              </div>
              <div class="text-sm text-neutral-700 font-medium mt-1">
                Page <span class="font-bold text-orange-600">{{ pagination.currentPage }}</span> of
                <span class="font-bold text-orange-600">{{ pagination.totalPages }}</span>
                &mdash;
                <span class="text-neutral-500">{{ pagination.totalRecords }} treatments</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
