<script setup lang="ts">
import Header from '@/components/user/Header.vue'
import HeroSection from '@/components/user/HeroSection.vue'
import HeroCalloutSection from '@/components/user/HeroCalloutSection.vue'
import FeaturesSection from '@/components/user/FeaturesSection.vue'
import TopProducts from '@/components/user/TopProducts.vue'
import TestimonialsSection from '@/components/user/TestimonialsSection.vue'
import HowItWorksSection from '@/components/user/HowItWorksSection.vue'
import FixItTogetherSection from '@/components/user/FixItTogetherSection.vue'
import FAQSection from '@/components/user/FAQSection.vue'
import CTASection from '@/components/user/CTASection.vue'
import Footer from '@/components/user/Footer.vue'
import { useAuthStore, useGlobalSettingsStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { computed, ref } from 'vue'
import { EditIcon, Loader2 } from 'lucide-vue-next'
import { useRoute } from 'vue-router'

const globalStore = useGlobalSettingsStore()
const { content, hasChanges } = storeToRefs(globalStore)

const authStore = useAuthStore()
const { isAuthenticated } = storeToRefs(authStore)

const route = useRoute()

const isEditingEnabled = computed(() => isAuthenticated.value && route.name === 'EditMainSite')

const updateContent = (section: string, newContent: any) => {
  globalStore.updateSection(section, newContent)
}

const isSavingChanges = ref(false)

const saveChanges = async () => {
  if (isSavingChanges.value) return
  isSavingChanges.value = true
  await globalStore.saveContent()
  isSavingChanges.value = false
}
</script>

<template>
  <div>
    <div v-if="isEditingEnabled && hasChanges" class="fixed bottom-4 right-4 z-50">
      <button
        class="bg-black text-white px-5 py-2.5 rounded-full shadow-2xl"
        :disabled="isSavingChanges"
        @click="saveChanges"
      >
        <Loader2 v-if="isSavingChanges" class="inline mr-2 h-4 w-4 animate-spin" />
        {{ isSavingChanges ? 'Saving...' : 'Save Changes' }}
      </button>
    </div>

    <div
      v-if="isAuthenticated && route.name !== 'EditMainSite'"
      class="fixed bottom-4 right-4 z-50"
    >
      <RouterLink
        :to="{ name: 'EditMainSite' }"
        target="_blank"
        class="bg-black text-white px-5 py-2.5 rounded-full shadow-2xl block"
      >
        <EditIcon class="inline mr-2 h-4 w-4" />
        Edit Page
      </RouterLink>
    </div>

    <Header
      :top-banner-content="content.topBanner"
      :is-admin="isEditingEnabled"
      @update:top-banner-content="updateContent('topBanner', $event)"
    />
    <HeroSection
      :content="content.hero"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('hero', $event)"
    />
    <HeroCalloutSection
      :content="content.heroCallout"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('heroCallout', $event)"
    />
    <FeaturesSection
      :content="content.features"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('features', $event)"
    />
    <TopProducts
      :content="content.productHighlights"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('productHighlights', $event)"
    />
    <TestimonialsSection
      :content="content.testimonials"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('testimonials', $event)"
    />
    <FixItTogetherSection
      :content="content.fixItTogether"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('fixItTogether', $event)"
    />
    <HowItWorksSection
      :content="content.howItWorks"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('howItWorks', $event)"
    />
    <FAQSection
      :content="content.faq"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('faq', $event)"
    />
    <CTASection
      :content="content.cta"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('cta', $event)"
    />
    <Footer
      :content="content.footer"
      :is-admin="isEditingEnabled"
      @update:content="updateContent('footer', $event)"
    />
  </div>
</template>
