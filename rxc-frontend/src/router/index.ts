import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '../layouts/MainLayout.vue'
import { defineMiddleware } from '@/middleware'
import { auth, guest } from '@/middleware/auth'
import { fetchCategories } from '@/middleware/landing'
import { useGlobalSettingsStore } from '@/stores'

const routes = [
  // user routes
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/user/LandingPage.vue'),
    beforeEnter: defineMiddleware(fetchCategories),
    meta: {
      title: 'Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/edit-main-site',
    name: 'EditMainSite',
    component: () => import('@/views/user/LandingPage.vue'),
    beforeEnter: defineMiddleware([auth, fetchCategories]),
    meta: {
      title: 'Editing Main Site',
    },
  },
  {
    path: '/treatments/:category_slug?/:sub_category_slug?',
    name: 'Treatments',
    component: () => import('@/views/user/Treatments.vue'),
    meta: {
      title: 'Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/treatment/:slug',
    name: 'TreatmentOverview',
    component: () => import('@/views/user/TreatmentOverview.vue'),
    meta: {
      title: 'Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },

  // Admin Login
  {
    path: '/admin/login',
    name: 'admin-login',
    component: () => import('@/views/admin/auth/LoginPage.vue'),
    beforeEnter: defineMiddleware(guest),
    meta: {
      title: 'Admin Login',
    },
  },

  // Admin Main Layout (Authenticated only)
  {
    path: '/admin',
    redirect: '/admin/dashboard',
    component: MainLayout,
    beforeEnter: defineMiddleware(auth),
    children: [
      // Dashboard
      {
        path: '/admin/dashboard',
        name: 'admin-dashboard',
        component: () => import('@/views/admin/dashboard/DashboardPage.vue'),
        meta: {
          title: 'Dashboard',
        },
      },

      // Categories Management
      {
        path: '/admin/categories',
        name: 'admin-categories',
        component: () => import('@/views/admin/category/CategoryListPage.vue'),
        meta: {
          title: 'Categories',
        },
      },

      // Sub-Categories Management
      {
        path: '/admin/sub-categories',
        name: 'admin-sub-categories',
        component: () => import('@/views/admin/category/SubCategoryListPage.vue'),
        meta: {
          title: 'Sub-Categories',
        },
      },

      // Product Management
      {
        path: '/admin/products',
        name: 'admin-products',
        component: () => import('@/views/admin/product/ProductListPage.vue'),
        meta: {
          title: 'Products',
        },
      },
      {
        path: '/admin/top-products',
        name: 'admin-top-products',
        component: () => import('@/views/admin/product/TopProductsPage.vue'),
        meta: {
          title: 'Top Products',
        },
      },
      {
        path: '/admin/products/add',
        name: 'admin-products-add',
        component: () => import('@/views/admin/product/ProductAddPage.vue'),
        meta: {
          title: 'Add Product',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },
      {
        path: '/admin/products/edit/:id',
        name: 'admin-products-edit',
        component: () => import('@/views/admin/product/ProductEditPage.vue'),
        meta: {
          title: 'Edit Product',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },
      {
        path: '/admin/products/view/:id',
        name: 'admin-products-view',
        component: () => import('@/views/admin/product/ProductViewPage.vue'),
        meta: {
          title: 'View Product',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },
      {
        path: '/admin/products/overview/:id',
        name: 'admin-products-overview',
        component: () => import('@/views/admin/product/ProductOverviewPage.vue'),
        meta: {
          title: 'Product Overview',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },

      // Account Settings
      {
        path: '/admin/account',
        name: 'admin-account',
        component: () => import('@/views/admin/account/AccountPage.vue'),
        meta: {
          title: 'Account Settings',
        },
      },

      // Application Settings
      {
        path: '/admin/settings',
        name: 'admin-settings',
        component: () => import('@/views/admin/settings/SettingsPage.vue'),
        meta: {
          title: 'Settings',
        },
      },
    ],
  },

  // 404 Error Page (Catch-all route)
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/admin/error/Error404Page.vue'),
    meta: {
      title: '404 Not Found',
    },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else if (from.path === to.path) {
      return { top: 0 }
    } else {
      return { top: 0 }
    }
  },
})

router.beforeEach((to, _, next) => {
  const settingsStore = useGlobalSettingsStore()

  if (settingsStore.settings?.app_name) {
    document.title =
      `${to.meta.title} - ${settingsStore.settings?.app_name}` || settingsStore.settings?.app_name
  } else {
    document.title = to.meta.title as string
  }

  next()
})

export default router
