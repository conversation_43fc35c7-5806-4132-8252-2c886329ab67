<script setup lang="ts">
import { ref, nextTick } from 'vue'
import EditButton from '@/components/user/EditButton.vue'
import FaqEditModal from '@/components/user/FaqEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const titleRef = ref<HTMLElement | null>(null)
const subtitleRef = ref<HTMLElement | null>(null)
const isFaqModalOpen = ref(false)
const openFaq = ref<number | null>(null)

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerHTML) {
    const newContent = { ...props.content, [field]: target.innerHTML }
    emit('update:content', newContent)
  }
}

const onFaqSave = (newFaqs: any) => {
  const newContent = { ...props.content, faqList: newFaqs }
  emit('update:content', newContent)
}

const toggle = (index: number) => {
  if (openFaq.value === index) {
    openFaq.value = null
  } else {
    openFaq.value = index
  }
}
</script>

<template>
  <!-- FAQ Section -->
  <section class="py-20 bg-gradient-to-b from-white via-gray-50/30 to-gray-50">
    <div class="max-w-7xl mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div class="lg:sticky lg:top-32">
          <div class="relative group inline-block">
            <h2
              ref="titleRef"
              class="text-4xl lg:text-5xl font-medium text-gray-900 mb-6"
              @blur="onTextBlur('title', $event)"
            >
              <span v-html="content.title"></span>
            </h2>
            <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
          </div>
          <div class="relative group">
            <p
              ref="subtitleRef"
              class="text-lg text-gray-600 mb-8"
              @blur="onTextBlur('subtitle', $event)"
            >
              {{ content.subtitle }}
            </p>
            <EditButton :is-admin="isAdmin" @edit="makeEditable(subtitleRef)" />
          </div>
        </div>
        <div class="space-y-4 relative group">
          <div
            v-for="(faq, index) in content.faqList"
            :key="index"
            class="border border-gray-200 rounded-lg"
          >
            <button
              @click="toggle(index)"
              class="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 transition-colors"
            >
              <span class="font-semibold text-gray-900">
                {{ faq.question }}
              </span>
              <svg
                class="w-6 h-6 text-gray-500 transition-transform duration-200"
                :class="{ 'rotate-45': openFaq === index }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                ></path>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300 ease-in-out"
              :class="{ 'max-h-96': openFaq === index, 'max-h-0': openFaq !== index }"
            >
              <div class="px-6 pb-6 text-gray-600">
                <p>{{ faq.answer }}</p>
              </div>
            </div>
          </div>
          <EditButton :is-admin="isAdmin" @edit="isFaqModalOpen = true" />
        </div>
      </div>
    </div>
    <FaqEditModal
      :is-open="isFaqModalOpen"
      :faqs="content.faqList"
      @close="isFaqModalOpen = false"
      @save="onFaqSave"
    />
  </section>
</template>
