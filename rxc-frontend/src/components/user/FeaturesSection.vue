<script setup lang="ts">
import { ref, nextTick } from 'vue'
import EditButton from '@/components/user/EditButton.vue'
import IconEditModal from '@/components/user/IconEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const titleRef = ref<HTMLElement | null>(null)
const subtitleRef = ref<HTMLElement | null>(null)
const featureTitleRefs = ref<(HTMLElement | null)[]>([])
const featureDescRefs = ref<(HTMLElement | null)[]>([])

const isIconModalOpen = ref(false)
const editingFeatureIndex = ref<number | null>(null)

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onFeatureBlur = (index: number, field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content.featureList[index][field] !== target.innerText) {
    const newFeatureList = [...props.content.featureList]
    newFeatureList[index] = { ...newFeatureList[index], [field]: target.innerText }
    const newContent = { ...props.content, featureList: newFeatureList }
    emit('update:content', newContent)
  }
}

const openIconModal = (index: number) => {
  editingFeatureIndex.value = index
  isIconModalOpen.value = true
}

const onIconSave = (newIcon: string) => {
  if (editingFeatureIndex.value !== null) {
    const newFeatureList = [...props.content.featureList]
    newFeatureList[editingFeatureIndex.value] = {
      ...newFeatureList[editingFeatureIndex.value],
      icon: newIcon,
    }
    const newContent = { ...props.content, featureList: newFeatureList }
    emit('update:content', newContent)
  }
  isIconModalOpen.value = false
  editingFeatureIndex.value = null
}
</script>

<template>
  <!-- Features Section -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-20">
        <div class="relative group inline-block">
          <h2
            ref="titleRef"
            class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6"
            @blur="onTextBlur('title', $event)"
          >
            {{ content.title }}
          </h2>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
        </div>
        <div class="relative group max-w-3xl mx-auto">
          <p
            ref="subtitleRef"
            class="text-xl text-neutral-600"
            @blur="onTextBlur('subtitle', $event)"
          >
            {{ content.subtitle }}
          </p>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(subtitleRef)" />
        </div>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="(feature, index) in content.featureList"
          :key="index"
          class="group p-8 rounded-2xl hover:bg-amber-50 transition-all duration-300 border border-gray-100 hover:border-amber-200"
        >
          <div class="relative">
            <div
              class="w-14 h-14 bg-neutral-100 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-amber-200 transition-colors"
            >
              <svg
                class="w-7 h-7 text-neutral-700"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  :d="feature.icon"
                ></path>
              </svg>
            </div>
            <EditButton :is-admin="isAdmin" @edit="openIconModal(index)" />
          </div>
          <div class="relative group">
            <h3
              :ref="(el) => (featureTitleRefs[index] = el as HTMLElement)"
              class="text-xl font-semibold text-neutral-900 mb-4"
              @blur="onFeatureBlur(index, 'title', $event)"
            >
              {{ feature.title }}
            </h3>
            <EditButton :is-admin="isAdmin" @edit="makeEditable(featureTitleRefs[index])" />
          </div>
          <div class="relative group">
            <p
              :ref="(el) => (featureDescRefs[index] = el as HTMLElement)"
              class="text-neutral-600 leading-relaxed"
              @blur="onFeatureBlur(index, 'description', $event)"
            >
              {{ feature.description }}
            </p>
            <EditButton :is-admin="isAdmin" @edit="makeEditable(featureDescRefs[index])" />
          </div>
        </div>
      </div>
    </div>
    <IconEditModal
      :is-open="isIconModalOpen"
      :icon="editingFeatureIndex !== null ? content.featureList[editingFeatureIndex].icon : ''"
      @close="isIconModalOpen = false"
      @save="onIconSave"
    />
  </section>
</template>
