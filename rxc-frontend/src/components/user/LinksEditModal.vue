<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { IconPlus, IconTrash } from '@tabler/icons-vue'

type Link = { title: string; href: string }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  links: {
    type: Array as () => Link[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableLinks = ref(props.links ? JSON.parse(JSON.stringify(props.links)) : [])

watch(
  () => props.links,
  (newVal) => {
    editableLinks.value = newVal ? JSON.parse(JSON.stringify(newVal)) : []
  },
)

const addLink = () => {
  editableLinks.value.push({ title: '', href: '' })
}

const removeLink = (index: number) => {
  editableLinks.value.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableLinks.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit Links</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <div
          v-for="(link, index) in editableLinks"
          :key="index"
          class="flex items-start gap-2 p-4 border rounded-lg"
        >
          <div class="grid gap-2 flex-1">
            <Label :for="`title-${index}`">Title</Label>
            <Input :id="`title-${index}`" v-model="link.title" />
            <Label :for="`href-${index}`">URL</Label>
            <Input :id="`href-${index}`" v-model="link.href" />
          </div>
          <Button variant="destructive" size="icon" @click="removeLink(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addLink">
          <IconPlus class="w-4 h-4 mr-2" />
          Add Link
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
