<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGlobalSettingsStore } from '@/stores'
import TopBanner from './TopBanner.vue'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'

const props = defineProps({
  topBannerContent: {
    type: Object,
    default: () => ({ items: [] }),
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:topBannerContent'])

const store = useGlobalSettingsStore()

const isSheetOpen = ref<boolean>(false)
const currentView = ref<string>('main') // 'main' or category name
const prevView = ref<string | null>(null)
const selectedCategory = ref<string | null>(null)

const categories = [
  {
    id: 'sexual-health',
    name: 'Sexual Health',
    treatments: [
      { name: 'Erectile dysfunction', slug: 'tadalafil-cialis' },
      { name: 'Early climax', slug: 'sildenafil-viagra' },
      { name: 'Hard Mints', slug: 'hard-mints' },
    ],
  },
  {
    id: 'weight-loss',
    name: 'Weight Loss',
    treatments: [
      { name: 'Compounded Semaglutide', slug: 'semaglutide' },
      { name: 'Compounded Tirzepatide', slug: 'tirzepatide' },
      { name: 'Wegovy', slug: 'wegovy' },
      { name: 'Ozempic', slug: 'ozempic' },
    ],
  },
  {
    id: 'hair-regrowth',
    name: 'Hair Regrowth',
    treatments: [
      { name: 'Finasteride', slug: 'finasteride' },
      { name: 'Minoxidil', slug: 'minoxidil' },
    ],
  },
  {
    id: 'mental-health',
    name: 'Mental Health',
    treatments: [
      { name: 'Anxiety Treatment', slug: 'anxiety-treatment' },
      { name: 'Depression Support', slug: 'depression-support' },
    ],
  },
  {
    id: 'skin',
    name: 'Skin',
    treatments: [
      { name: 'Acne Treatment', slug: 'acne-treatment' },
      { name: 'Anti-aging', slug: 'anti-aging' },
    ],
  },
  {
    id: 'everyday-health',
    name: 'Everyday Health',
    treatments: [
      { name: 'Vitamins', slug: 'vitamins' },
      { name: 'Supplements', slug: 'supplements' },
    ],
  },
]

const topTreatments: { name: string; slug: string; badge?: string }[] = [
  // { name: 'Oral Medication Kits', slug: 'oral-medication-kits', badge: 'NEW' },
  // { name: 'Hard Mints™ by Hims', slug: 'hard-mints', badge: 'NEW' },
]

const openCategory = (categoryId: string) => {
  // record where we came from to choose animation direction
  prevView.value = currentView.value
  selectedCategory.value = categoryId
  currentView.value = 'category'
}

const goBackToMain = () => {
  prevView.value = currentView.value
  currentView.value = 'main'
  selectedCategory.value = null
}

const onSheetOpenChange = (open: boolean) => {
  isSheetOpen.value = open
  if (!open) {
    // reset view when the sheet closes
    prevView.value = currentView.value
    currentView.value = 'main'
    selectedCategory.value = null
  }
}

const transitionName = computed(() => {
  // Decide slide direction based on where we came from
  if (prevView.value === 'main' && currentView.value === 'category') return 'slide-left'
  if (prevView.value === 'category' && currentView.value === 'main') return 'slide-right'
  return 'fade'
})

const getCurrentCategory = () => {
  return categories.find((cat) => cat.id === selectedCategory.value)
}
</script>

<template>
  <TopBanner
    :content="props.topBannerContent"
    :is-admin="props.isAdmin"
    @update:content="emit('update:topBannerContent', $event)"
  />

  <!-- navbar -->
  <nav
    class="sticky top-0 z-50 bg-gradient-to-r from-white/95 via-gray-50/95 to-white/95 backdrop-blur-sm border-b border-gray-200/30"
  >
    <!-- Main Navigation -->
    <div class="max-w-7xl mx-auto px-4">
      <div class="flex justify-between items-center py-4">
        <!-- Logo -->
        <RouterLink :to="{ name: 'Home' }" class="flex-shrink-0">
          <img
            :src="store.settings?.app_logo"
            :alt="store.settings?.app_name"
            class="max-h-12 max-w-36 sm:max-w-56"
          />
        </RouterLink>

        <!-- Right side buttons -->
        <div class="flex items-center space-x-0 sm:space-x-2">
          <!-- Get Care Button -->
          <RouterLink
            :to="{ name: 'Treatments' }"
            class="px-3 py-2 sm:px-6 sm:py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors text-xs sm:text-base"
          >
            Get Care
          </RouterLink>

          <!-- Sheet Menu -->
          <Sheet v-model:open="isSheetOpen" @update:open="onSheetOpenChange">
            <SheetTrigger as-child>
              <button class="p-2">
                <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                  <div
                    class="w-5 h-0.5 bg-gray-600 transition-all"
                    :class="{
                      'rotate-45 translate-y-1.5': isSheetOpen,
                    }"
                  ></div>
                  <div
                    class="w-5 h-0.5 bg-gray-600 transition-all"
                    :class="{ 'opacity-0': isSheetOpen }"
                  ></div>
                  <div
                    class="w-5 h-0.5 bg-gray-600 transition-all"
                    :class="{
                      '-rotate-45 -translate-y-1.5': isSheetOpen,
                    }"
                  ></div>
                </div>
              </button>
            </SheetTrigger>
            <!-- increase the svg size inside button -->
            <SheetContent
              side="right"
              class="p-0 rounded-tl-2xl rounded-bl-2xl gap-0 sheet-content-polish [&>button:first-of-type]:p-1 [&>button:first-of-type>svg]:size-5"
            >
              <SheetHeader class="sticky top-0 bg-white flex-row items-center rounded-tl-2xl">
                <!-- Back button (only show in category view) -->
                <button
                  v-if="currentView === 'category'"
                  @click="goBackToMain"
                  class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 19l-7-7 7-7"
                    ></path>
                  </svg>
                </button>

                <!-- Title -->
                <SheetTitle
                  v-if="currentView === 'main'"
                  class="text-xl font-semibold text-gray-900"
                >
                  Menu
                </SheetTitle>
              </SheetHeader>

              <!-- Sheet Content -->
              <div class="flex-1 overflow-y-auto">
                <transition :name="transitionName" mode="out-in">
                  <!-- Main Menu View -->
                  <div v-if="currentView === 'main'" key="main" class="px-6 py-6">
                    <!-- Explore Section -->
                    <div class="mb-8">
                      <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
                        EXPLORE
                      </h3>
                      <div class="space-y-1">
                        <button
                          v-for="category in categories"
                          :key="category.id"
                          @click="openCategory(category.id)"
                          class="flex items-center justify-between w-full px-4 py-3 text-left text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                        >
                          <span class="font-medium">{{ category.name }}</span>
                          <svg
                            class="w-5 h-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 5l7 7-7 7"
                            ></path>
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- Top Treatments Section -->
                    <div v-if="topTreatments.length">
                      <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
                        TOP TREATMENTS
                      </h3>
                      <div class="space-y-4">
                        <div
                          v-for="treatment in topTreatments"
                          :key="treatment.slug"
                          class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer"
                        >
                          <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">{{ treatment.name }}</h4>
                            <span
                              v-if="treatment.badge"
                              class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"
                            >
                              {{ treatment.badge }}
                            </span>
                          </div>
                          <!-- Placeholder for treatment image -->
                          <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Category View -->
                  <div v-else key="category" class="px-6 py-6">
                    <div v-if="getCurrentCategory()">
                      <h3 class="text-2xl font-semibold text-gray-900 mb-4">
                        {{ getCurrentCategory()?.name }}
                      </h3>
                      <!-- Category Header with Image -->
                      <div class="mb-6">
                        <div class="bg-gray-100 rounded-lg p-6 mb-4">
                          <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            Start your {{ getCurrentCategory()?.name }} assessment
                          </h3>
                          <!-- Placeholder for category image -->
                          <div class="w-32 h-32 bg-gray-200 rounded-lg mx-auto"></div>
                        </div>
                        <button
                          class="w-full bg-black text-white py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
                        >
                          Get Started
                        </button>
                      </div>

                      <!-- Explore Section -->
                      <div class="mb-6">
                        <h4
                          class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3"
                        >
                          EXPLORE
                        </h4>
                        <div class="space-y-2">
                          <div
                            v-for="treatment in getCurrentCategory()?.treatments"
                            :key="treatment.slug"
                            class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
                          >
                            <RouterLink
                              :to="{ name: 'TreatmentOverview', params: { slug: treatment.slug } }"
                              @click="isSheetOpen = false"
                              class="text-gray-900 hover:text-gray-600 transition-colors"
                            >
                              {{ treatment.name }}
                            </RouterLink>
                            <svg
                              class="w-4 h-4 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 5l7 7-7 7"
                              ></path>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  </nav>
</template>

<style scoped>
.sheet-content-polish {
  box-shadow: -10px 20px 40px rgba(15, 23, 42, 0.06);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.98), #ffffff);
}

/* Slide left: main -> category */
.slide-left-enter-active,
.slide-left-leave-active {
  transition:
    transform 260ms cubic-bezier(0.2, 0.9, 0.3, 1),
    opacity 200ms ease;
}
.slide-left-enter-from {
  transform: translateX(20px);
  opacity: 0;
}
.slide-left-enter-to {
  transform: translateX(0);
  opacity: 1;
}
.slide-left-leave-from {
  transform: translateX(0);
  opacity: 1;
}
.slide-left-leave-to {
  transform: translateX(-12px);
  opacity: 0;
}

/* Slide right: category -> main */
.slide-right-enter-active,
.slide-right-leave-active {
  transition:
    transform 260ms cubic-bezier(0.2, 0.9, 0.3, 1),
    opacity 200ms ease;
}
.slide-right-enter-from {
  transform: translateX(-20px);
  opacity: 0;
}
.slide-right-enter-to {
  transform: translateX(0);
  opacity: 1;
}
.slide-right-leave-from {
  transform: translateX(0);
  opacity: 1;
}
.slide-right-leave-to {
  transform: translateX(12px);
  opacity: 0;
}

/* Fallback fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 160ms ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>
