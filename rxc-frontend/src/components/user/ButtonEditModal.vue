<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  href: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableText = ref(props.text)
const editableHref = ref(props.href)

watch(
  () => props.text,
  (newVal) => {
    editableText.value = newVal
  },
)

watch(
  () => props.href,
  (newVal) => {
    editableHref.value = newVal
  },
)

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', { text: editableText.value, href: editableHref.value })
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Edit Button</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="text" class="text-right"> Text </Label>
          <Input id="text" v-model="editableText" class="col-span-3" />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="href" class="text-right"> Link (href) </Label>
          <Input id="href" v-model="editableHref" class="col-span-3" />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
