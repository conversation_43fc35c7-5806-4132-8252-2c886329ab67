<script setup lang="ts">
import { IconArrowRight } from '@tabler/icons-vue'
import { ref, nextTick } from 'vue'
import EditButton from '@/components/user/EditButton.vue'
import ImageUploadModal from '@/components/user/ImageUploadModal.vue'
import ButtonEditModal from '@/components/user/ButtonEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const titleRef = ref<HTMLElement | null>(null)
const cardTagRefs = ref<(HTMLElement | null)[]>([])
const cardTitleRefs = ref<(HTMLElement | null)[]>([])
const isImageModalOpen = ref(false)
const isButtonModalOpen = ref(false)
const editingCardIndex = ref<number | null>(null)

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onCardTagBlur = (index: number, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content.cards[index].tag !== target.innerText) {
    const newCards = [...props.content.cards]
    newCards[index] = { ...newCards[index], tag: target.innerText }
    const newContent = { ...props.content, cards: newCards }
    emit('update:content', newContent)
  }
}

const onCardTitleBlur = (index: number, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content.cards[index].title !== target.innerHTML) {
    const newCards = [...props.content.cards]
    newCards[index] = { ...newCards[index], title: target.innerHTML }
    const newContent = { ...props.content, cards: newCards }
    emit('update:content', newContent)
  }
}

const openImageModal = (index: number) => {
  editingCardIndex.value = index
  isImageModalOpen.value = true
}

const onImageSave = (newImageUrl: string) => {
  if (editingCardIndex.value !== null) {
    const newCards = [...props.content.cards]
    newCards[editingCardIndex.value] = {
      ...newCards[editingCardIndex.value],
      backgroundImage: newImageUrl,
    }
    const newContent = { ...props.content, cards: newCards }
    emit('update:content', newContent)
  }
}

const openButtonModal = (index: number) => {
  editingCardIndex.value = index
  isButtonModalOpen.value = true
}

const onButtonSave = (newButtonData: { text: string; href: string }) => {
  if (editingCardIndex.value !== null) {
    const newCards = [...props.content.cards]
    newCards[editingCardIndex.value] = {
      ...newCards[editingCardIndex.value],
      buttonText: newButtonData.text,
      link: newButtonData.href,
    }
    const newContent = { ...props.content, cards: newCards }
    emit('update:content', newContent)
  }
}
</script>

<template>
  <!-- Let's Fix It Together Section -->
  <section class="py-12 pb-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-10 sm:mb-20 relative group">
        <h2
          ref="titleRef"
          class="text-4xl lg:text-6xl font-bold text-neutral-900"
          @blur="onTextBlur('title', $event)"
        >
          {{ content.title }}
        </h2>
        <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
      </div>

      <div class="grid md:grid-cols-3 gap-6">
        <div
          v-for="(card, index) in content.cards"
          :key="index"
          class="relative rounded-3xl min-h-[500px] group"
        >
          <div class="absolute inset-0 bg-black/20 rounded-3xl"></div>
          <div class="absolute inset-0 group rounded-3xl">
            <img
              :src="card.backgroundImage"
              :alt="card.tag"
              class="absolute inset-0 w-full h-full object-cover transition-transform duration-300 rounded-3xl"
            />
            <EditButton :is-admin="isAdmin" @edit="openImageModal(index)" />
          </div>
          <div class="relative z-10 p-8 h-full flex flex-col justify-between">
            <div class="space-y-4">
              <div class="relative group">
                <div
                  :ref="(el) => (cardTagRefs[index] = el as HTMLElement)"
                  class="bg-white/90 backdrop-blur-sm text-black text-sm font-medium px-3 py-1 rounded-full w-fit"
                  @blur="onCardTagBlur(index, $event)"
                >
                  {{ card.tag }}
                  <EditButton :is-admin="isAdmin" @edit="makeEditable(cardTagRefs[index])" />
                </div>
              </div>
              <div class="relative group">
                <h3
                  :ref="(el) => (cardTitleRefs[index] = el as HTMLElement)"
                  class="text-white text-5xl font-bold mb-6 leading-tight text-shadow-md"
                  :class="{ 'text-black': index === 1 }"
                  @blur="onCardTitleBlur(index, $event)"
                >
                  <span v-html="card.title"></span>
                </h3>
                <EditButton :is-admin="isAdmin" @edit="makeEditable(cardTitleRefs[index])" />
              </div>
            </div>
            <div class="relative group inline-block">
              <RouterLink
                :to="card.link"
                class="bg-yellow-300 hover:bg-yellow-200 text-black font-semibold px-6 py-3 rounded-full transition-all duration-300 inline-flex items-center"
              >
                {{ card.buttonText || 'Get care' }}
                <IconArrowRight
                  class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                />
              </RouterLink>
              <EditButton :is-admin="isAdmin" @edit="openButtonModal(index)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <ImageUploadModal
      :is-open="isImageModalOpen"
      @close="isImageModalOpen = false"
      @save="onImageSave"
    />
    <ButtonEditModal
      v-if="editingCardIndex !== null"
      :is-open="isButtonModalOpen"
      :text="content.cards[editingCardIndex].buttonText || 'Get care'"
      :href="content.cards[editingCardIndex].link"
      @close="isButtonModalOpen = false"
      @save="onButtonSave"
    />
  </section>
</template>
