<script setup lang="ts">
import { ref, nextTick } from 'vue'
import EditButton from '@/components/user/EditButton.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const titleRef = ref<HTMLElement | null>(null)
const subtitleRef = ref<HTMLElement | null>(null)
const testimonialNameRefs = ref<(HTMLElement | null)[]>([])
const testimonialReviewRefs = ref<(HTMLElement | null)[]>([])

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onTestimonialBlur = (index: number, field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  let value = target.innerText
  if (field === 'review') {
    value = value.replace(/^"|"$/g, '')
  }
  if (props.content.testimonialList[index][field] !== value) {
    const newTestimonialList = [...props.content.testimonialList]
    newTestimonialList[index] = { ...newTestimonialList[index], [field]: value }
    const newContent = { ...props.content, testimonialList: newTestimonialList }
    emit('update:content', newContent)
  }
}
</script>

<template>
  <!-- Testimonials -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-20">
        <div class="relative group inline-block">
          <h2
            ref="titleRef"
            class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6"
            @blur="onTextBlur('title', $event)"
          >
            {{ content.title }}
          </h2>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
        </div>
        <div class="relative group">
          <p
            ref="subtitleRef"
            class="text-xl text-neutral-600"
            @blur="onTextBlur('subtitle', $event)"
          >
            {{ content.subtitle }}
          </p>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(subtitleRef)" />
        </div>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="(testimonial, index) in content.testimonialList"
          :key="index"
          class="bg-neutral-50 p-8 rounded-3xl border border-neutral-200"
        >
          <div class="flex items-center mb-6">
            <div v-for="i in 5" :key="i" class="text-yellow-400 mr-1">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                ></path>
              </svg>
            </div>
          </div>
          <div class="relative group">
            <p
              :ref="(el) => (testimonialNameRefs[index] = el as HTMLElement)"
              class="font-semibold text-neutral-900 mb-3"
              @blur="onTestimonialBlur(index, 'name', $event)"
            >
              {{ testimonial.name }}
            </p>
            <EditButton :is-admin="isAdmin" @edit="makeEditable(testimonialNameRefs[index])" />
          </div>
          <div class="relative group">
            <p
              :ref="(el) => (testimonialReviewRefs[index] = el as HTMLElement)"
              class="text-neutral-700 leading-relaxed text-lg"
              @blur="onTestimonialBlur(index, 'review', $event)"
            >
              "{{ testimonial.review }}"
            </p>
            <EditButton :is-admin="isAdmin" @edit="makeEditable(testimonialReviewRefs[index])" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
