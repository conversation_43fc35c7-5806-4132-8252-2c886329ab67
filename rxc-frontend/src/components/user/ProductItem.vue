<script setup lang="ts">
import type { Product } from '@/types/api'
import { ref } from 'vue'
import ProgramItemsModal from './ProgramItemsModal.vue'
import { capitalize } from '@/lib'

defineProps<{ product: Product }>()

const isModalOpen = ref(false)
</script>

<template>
  <RouterLink
    v-if="product"
    :to="{ name: 'TreatmentOverview', params: { slug: product.slug } }"
    class="relative bg-neutral-50 rounded-2xl p-6 flex flex-col min-h-[420px] overflow-hidden hover:shadow-lg transition-all duration-300 border border-neutral-200"
  >
    <div class="flex justify-between mb-4">
      <div class="text-xs font-medium px-2 py-1 rounded bg-amber-50 text-amber-700">
        {{ product.main_category_name }}
      </div>
      <div>
        <span
          class="inline-block text-green-700 bg-green-50 border border-green-200 text-xs px-3 py-0.5 rounded"
        >
          In Stock
        </span>
        <!-- <span
          v-else
          class="inline-block text-red-700 bg-red-50 border border-red-200 text-xs px-3 py-0.5 rounded"
        >
          Out of Stock</span
        > -->
      </div>
    </div>
    <div>
      <h3 class="text-xl font-medium text-neutral-900 mb-2 z-10 relative">
        {{ product.product_name_title }}
      </h3>
      <div
        v-if="product.product_type === 'single'"
        class="text-sm text-neutral-600 mb-2 leading-relaxed z-10 relative space-x-2"
      >
        <span
          v-if="product.product_form"
          class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
          >{{ capitalize(product.product_form) }}</span
        >
        <span
          v-if="product.product_strength"
          class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
          >{{ product.product_strength }}
        </span>
        <!-- <span
          v-if="product.product_qty"
          class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
          >{{ product.product_qty }}
        </span> -->
      </div>
    </div>
    <div class="flex justify-center items-center h-full">
      <img
        :src="product.image"
        :alt="product.product_name_title"
        class="w-5/7 min-w-[120px] max-w-[330px] object-contain z-0 select-none pointer-events-none"
      />
    </div>
    <div class="mt-auto gap-3 z-10 relative flex flex-col-reverse justify-end">
      <div class="text-center">
        <RouterLink
          :to="{ name: 'TreatmentOverview', params: { slug: product.slug } }"
          class="text-black border-b border-black font-medium cursor-pointer w-full hover:no-underline transition-all duration-200"
        >
          Learn&nbsp;more
          <!-- <ArrowRight class="size-4 inline-block" /> -->
        </RouterLink>
      </div>
      <template v-if="product.product_type === 'single'">
        <a
          :href="product.xpedicare_url"
          @click.stop="() => {}"
          class="bg-white text-black border border-gray-400 px-5 py-2 rounded-full hover:bg-gray-800 hover:text-white transition-all duration-200 font-medium cursor-pointer text-center w-full"
        >
          Get&nbsp;Started
        </a>
      </template>
      <template v-if="product.product_type === 'programs'">
        <button
          class="bg-white text-black border border-gray-400 px-5 py-2 rounded-full hover:bg-gray-800 hover:text-white transition-all duration-200 font-medium cursor-pointer text-center w-full"
          @click.prevent="isModalOpen = true"
        >
          Get&nbsp;Started
        </button>
      </template>
    </div>
  </RouterLink>
  <ProgramItemsModal
    v-if="product && product.product_type === 'programs'"
    :open="isModalOpen"
    :product-name="product.product_name_title"
    :items="product.product_items"
    @update:open="isModalOpen = $event"
  />
</template>
