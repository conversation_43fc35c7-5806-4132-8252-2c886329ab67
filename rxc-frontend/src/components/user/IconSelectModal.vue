<script setup lang="ts">
import { ref, computed } from 'vue'
import { icons } from '@/lib/icons'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

defineProps({
  modelValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const searchTerm = ref('')

const filteredIcons = computed(() => {
  if (!searchTerm.value) {
    return icons
  }
  return icons.filter((icon) => icon.name.toLowerCase().includes(searchTerm.value.toLowerCase()))
})

const selectIcon = (iconPath: string) => {
  emit('update:modelValue', iconPath)
}

const clearSelection = () => {
  emit('update:modelValue', '')
}
</script>
<template>
  <div class="p-4">
    <Input v-model="searchTerm" placeholder="Search icons..." class="mb-4" />
    <div class="grid grid-cols-6 gap-4 max-h-64 overflow-y-auto">
      <div
        v-for="icon in filteredIcons"
        :key="icon.name"
        class="p-2 border rounded-lg flex items-center justify-center cursor-pointer"
        :class="{ 'bg-blue-100 border-blue-500': modelValue === icon.path }"
        @click="selectIcon(icon.path)"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            :d="icon.path"
          ></path>
        </svg>
      </div>
    </div>
    <div class="flex justify-end mt-4">
      <Button variant="outline" @click="clearSelection">Clear</Button>
    </div>
  </div>
</template>
