<script setup lang="ts">
import { IconArrowRight } from '@tabler/icons-vue'
import { ref, nextTick, type Ref } from 'vue'
import ImageUploadModal from '@/components/user/ImageUploadModal.vue'
import ButtonEditModal from '@/components/user/ButtonEditModal.vue'
import EditButton from '@/components/user/EditButton.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const isImageModalOpen = ref(false)
const isButtonModalOpen = ref(false)
const titleRef = ref<HTMLElement | null>(null) as Ref<HTMLElement | null>

const onImageSave = (newImageUrl: string) => {
  const newContent = { ...props.content, backgroundImage: newImageUrl }
  emit('update:content', newContent)
}

const onButtonSave = (newButtonData: { text: string; href: string }) => {
  const newContent = {
    ...props.content,
    buttonText: newButtonData.text,
    buttonLink: newButtonData.href,
  }
  emit('update:content', newContent)
}

const makeEditable = async () => {
  if (titleRef.value) {
    titleRef.value.contentEditable = 'true'
    await nextTick()
    titleRef.value.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerHTML) {
    const newContent = { ...props.content, [field]: target.innerHTML }
    emit('update:content', newContent)
  }
}
</script>

<template>
  <!-- Hero Callout Section -->
  <section class="relative py-24 max-w-7xl mx-auto sm:rounded-2xl mb-10 group">
    <div class="absolute inset-0 z-0">
      <img
        :src="content.backgroundImage"
        alt="Romantic background"
        class="w-full h-full object-cover sm:rounded-2xl"
      />
      <div class="absolute inset-0 bg-red-900/40 sm:rounded-2xl"></div>
      <EditButton :is-admin="isAdmin" @edit="isImageModalOpen = true" />
    </div>
    <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="relative group inline-block">
        <h2
          ref="titleRef"
          class="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight"
          @blur="onTextBlur('title', $event)"
        >
          {{ content.title }}
        </h2>
        <EditButton :is-admin="isAdmin" @edit="makeEditable" />
      </div>
      <div class="mt-12 relative group inline-block">
        <RouterLink
          :to="content.buttonLink"
          class="bg-yellow-300 hover:bg-yellow-200 text-black font-semibold px-8 py-4 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center cursor-pointer"
        >
          <span>{{ content.buttonText }}</span>
          <IconArrowRight class="w-5 h-5 ml-2" />
        </RouterLink>
        <EditButton :is-admin="isAdmin" @edit="isButtonModalOpen = true" />
      </div>
    </div>
    <ImageUploadModal
      :is-open="isImageModalOpen"
      @close="isImageModalOpen = false"
      @save="onImageSave"
    />
    <ButtonEditModal
      :is-open="isButtonModalOpen"
      :text="content.buttonText"
      :href="content.buttonLink"
      @close="isButtonModalOpen = false"
      @save="onButtonSave"
    />
  </section>
</template>
