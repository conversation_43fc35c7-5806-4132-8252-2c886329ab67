<script setup lang="ts">
import { useGlobalSettingsStore } from '@/stores'
import { ref, nextTick } from 'vue'
import EditButton from '@/components/user/EditButton.vue'
import ButtonEditModal from '@/components/user/ButtonEditModal.vue'
import IconEditModal from '@/components/user/IconEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  startVisitUrl: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:content'])

const store = useGlobalSettingsStore()

const titleRef = ref<HTMLElement | null>(null)
const subtitleRef = ref<HTMLElement | null>(null)
const stepTitleRefs = ref<(HTMLElement | null)[]>([])
const stepDescRefs = ref<(HTMLElement | null)[]>([])
const isButtonModalOpen = ref(false)
const isIconModalOpen = ref(false)
const editingStepIndex = ref<number | null>(null)

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onStepBlur = (index: number, field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content.steps[index][field] !== target.innerText) {
    const newSteps = [...props.content.steps]
    newSteps[index] = { ...newSteps[index], [field]: target.innerText }
    const newContent = { ...props.content, steps: newSteps }
    emit('update:content', newContent)
  }
}

const onButtonSave = (newButtonData: { text: string; href: string }) => {
  const newContent = {
    ...props.content,
    buttonText: newButtonData.text,
    buttonLink: newButtonData.href,
  }
  emit('update:content', newContent)
}

const openIconModal = (index: number) => {
  editingStepIndex.value = index
  isIconModalOpen.value = true
}

const onIconSave = (newIcon: string) => {
  if (editingStepIndex.value !== null) {
    const newSteps = [...props.content.steps]
    newSteps[editingStepIndex.value] = {
      ...newSteps[editingStepIndex.value],
      icon: newIcon,
    }
    const newContent = { ...props.content, steps: newSteps }
    emit('update:content', newContent)
  }
  isIconModalOpen.value = false
  editingStepIndex.value = null
}
</script>

<template>
  <!-- How it Works Section -->
  <section class="py-24 bg-neutral-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-20">
        <div class="relative group inline-block">
          <h2
            ref="titleRef"
            class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6"
            @blur="onTextBlur('title', $event)"
          >
            {{ content.title.replace('RxConnexion', store.settings?.app_name || 'RxConnexion') }}
          </h2>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
        </div>
        <div class="relative group max-w-3xl mx-auto">
          <p
            ref="subtitleRef"
            class="text-xl text-neutral-600"
            @blur="onTextBlur('subtitle', $event)"
          >
            {{ content.subtitle }}
          </p>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(subtitleRef)" />
        </div>
      </div>

      <div class="grid md:grid-cols-3 gap-8 lg:gap-12">
        <div v-for="(step, index) in content.steps" :key="step.step" class="relative group">
          <!-- Step Card -->
          <div
            :class="[
              'relative p-8 rounded-3xl border border-neutral-200 bg-gradient-to-br transition-all duration-300 hover:shadow-lg hover:scale-105',
              step.color,
            ]"
          >
            <!-- Step Number -->
            <div
              :class="[
                'inline-flex items-center justify-center w-12 h-12 rounded-2xl font-bold text-lg mb-6',
                step.stepColor,
                'bg-white/80 backdrop-blur-sm',
              ]"
            >
              {{ step.step }}
            </div>

            <!-- Icon -->
            <div class="relative">
              <div
                :class="[
                  'w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300',
                  step.iconBg,
                  'group-hover:scale-110',
                ]"
              >
                <svg
                  :class="['w-8 h-8', step.iconColor]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    :d="step.icon"
                  ></path>
                </svg>
              </div>
              <EditButton :is-admin="isAdmin" @edit="openIconModal(index)" />
            </div>

            <!-- Content -->
            <div class="relative group">
              <h3
                :ref="(el) => (stepTitleRefs[index] = el as HTMLElement)"
                class="text-2xl font-bold text-neutral-900 mb-4"
                @blur="onStepBlur(index, 'title', $event)"
              >
                {{ step.title }}
              </h3>
              <EditButton :is-admin="isAdmin" @edit="makeEditable(stepTitleRefs[index])" />
            </div>
            <div class="relative group">
              <p
                :ref="(el) => (stepDescRefs[index] = el as HTMLElement)"
                class="text-neutral-700 leading-relaxed text-lg"
                @blur="onStepBlur(index, 'description', $event)"
              >
                {{ step.description }}
              </p>
              <EditButton :is-admin="isAdmin" @edit="makeEditable(stepDescRefs[index])" />
            </div>
          </div>
        </div>
      </div>

      <!-- Call to Action -->
      <div class="text-center mt-16">
        <div class="relative group inline-block">
          <a
            v-if="props.startVisitUrl"
            :href="props.startVisitUrl"
            class="inline-flex items-center bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg"
          >
            Continue Online Visit
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              ></path>
            </svg>
          </a>
          <RouterLink
            v-else
            :to="content.buttonLink"
            class="inline-flex items-center bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg"
          >
            {{ content.buttonText }}
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              ></path>
            </svg>
          </RouterLink>
          <EditButton :is-admin="isAdmin" @edit="isButtonModalOpen = true" />
        </div>
      </div>
    </div>
    <ButtonEditModal
      :is-open="isButtonModalOpen"
      :text="content.buttonText"
      :href="content.buttonLink"
      @close="isButtonModalOpen = false"
      @save="onButtonSave"
    />
    <IconEditModal
      :is-open="isIconModalOpen"
      :icon="editingStepIndex !== null ? content.steps[editingStepIndex].icon : ''"
      @close="isIconModalOpen = false"
      @save="onIconSave"
    />
  </section>
</template>
