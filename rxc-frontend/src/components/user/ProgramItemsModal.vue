<script setup lang="ts">
import type { ProgramProductItem } from '@/types/api'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { capitalize, formatCurrency } from '@/lib'

defineProps<{
  open: boolean
  productName: string
  items: ProgramProductItem[]
}>()

const emit = defineEmits(['update:open'])

const closeModal = () => {
  emit('update:open', false)
}
</script>

<template>
  <Dialog :open="open" @update:open="closeModal">
    <DialogContent class="sm:max-w-[625px]">
      <DialogHeader>
        <DialogTitle>Available in {{ productName }}</DialogTitle>
        <DialogDescription> Choose one of the following options to get started. </DialogDescription>
      </DialogHeader>
      <div class="grid gap-4 py-4">
        <div
          v-for="item in items"
          :key="item.product_id"
          class="flex justify-between items-center p-4 border rounded-lg"
        >
          <div>
            <h4 class="font-semibold">{{ item.product_name }}</h4>
            <div class="text-sm text-neutral-600 space-x-2 mt-1">
              <span
                v-if="item.product_form"
                class="px-2.5 py-0.5 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                >{{ capitalize(item.product_form) }}</span
              >
              <span
                v-if="item.product_strength"
                class="px-2.5 py-0.5 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                >{{ item.product_strength }}</span
              >
            </div>
            <div class="text-sm font-medium mt-3">{{ formatCurrency(item.price) }}</div>
          </div>
          <a
            :href="item.xpedicare_url"
            target="_blank"
            class="bg-black text-white px-4 py-2 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer text-center text-sm"
          >
            Get Started
          </a>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal"> Close </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
