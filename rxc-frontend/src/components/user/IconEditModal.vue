<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import IconSelectModal from '@/components/user/IconSelectModal.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  icon: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['close', 'save'])

const selectedIcon = ref('')

watch(
  () => props.icon,
  (newIcon) => {
    selectedIcon.value = newIcon
  },
)

const closeModal = () => {
  emit('close')
}

const saveIcon = () => {
  emit('save', selectedIcon.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Edit Icon</DialogTitle>
        <DialogDescription> Select an icon from the list below. </DialogDescription>
      </DialogHeader>
      <IconSelectModal v-model="selectedIcon" />
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="saveIcon">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
