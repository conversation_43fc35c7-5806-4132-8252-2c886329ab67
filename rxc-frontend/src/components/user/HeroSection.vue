<script setup lang="ts">
import { IconArrowRight } from '@tabler/icons-vue'
import { computed, onMounted, onUnmounted, ref, nextTick } from 'vue'
import { useGlobalSettingsStore } from '@/stores'
import { storeToRefs } from 'pinia'
import PhrasesEditModal from '@/components/user/PhrasesEditModal.vue'
import EditButton from '@/components/user/EditButton.vue'
import IconEditModal from '@/components/user/IconEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const store = useGlobalSettingsStore()
const { categories: categoriesArr } = storeToRefs(store)

const currentPhrase = ref<string>(props.content.phrases[0])
const isPhrasesModalOpen = ref(false)
const isIconModalOpen = ref(false)
const editingFeatureIndex = ref<number | null>(null)
const titleRef = ref<HTMLElement | null>(null)
const subtitleRef = ref<HTMLElement | null>(null)
const featureRefs = ref<(HTMLElement | null)[]>([])

const categories = computed(() =>
  categoriesArr.value.map((category, idx) => ({
    ...category,
    bgClass: getBgColorForIndex(idx),
    textClass: getTextColorForIndex(idx),
  })),
)

function getBgColorForIndex(index: number) {
  const colors = [
    'from-[#fef6e7] via-[#fdf3e6] to-[#fbeeea] border-amber-200', // amber
    'from-[#e7eafc] via-[#f0f3fd] to-[#eaf3fb] border-blue-100', // blue
    'from-[#e7fcf3] via-[#e6fdf3] to-[#eafbf7] border-green-100', // green
    'from-[#f3e7fc] via-[#f3e6fd] to-[#fbf7ea] border-purple-100', // purple
    'from-[#f7e7fc] via-[#f6e6fd] to-[#faebf7] border-pink-100', // pink
    'from-[#fcf3e7] via-[#f6e6fd] to-[#faebf7] border-red-100', // red
    'from-[#e7f3fc] via-[#e6f0fd] to-[#ebf7fa] border-cyan-100', // cyan
    'from-[#f3fcf7] via-[#f0fded] to-[#ebf7f3] border-mint-100', // mint
    'from-[#f3e7f7] via-[#f3e6fd] to-[#fbf7ea] border-light-purple-100', // light purple
  ]

  return colors[index % colors.length]
}

function getTextColorForIndex(index: number) {
  const colors = [
    'text-amber-800', // amber
    'text-blue-800', // blue
    'text-green-800', // green
    'text-purple-800', // purple
    'text-pink-800', // pink
    'text-red-800', // red
    'text-cyan-800', // cyan
    'text-mint-800', // mint
    'text-light-purple-800', // light purple
  ]

  return colors[index % colors.length]
}

const isOdd = computed(() => categories.value.length % 2 === 1)
const featuredIndex = computed(() => (isOdd.value ? 0 : -1)) // feature the first card by default

let index = 0
let intervalId: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  intervalId = setInterval(() => {
    index = (index + 1) % props.content.phrases.length
    currentPhrase.value = props.content.phrases[index]
  }, 2500)
})

onUnmounted(() => {
  if (intervalId) clearInterval(intervalId)
})

const onPhrasesSave = (newPhrases: string[]) => {
  const newContent = { ...props.content, phrases: newPhrases }
  emit('update:content', newContent)
}

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onFeatureBlur = (index: number, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content.features[index].text !== target.innerText) {
    const newFeatures = [...props.content.features]
    newFeatures[index] = { ...newFeatures[index], text: target.innerText }
    const newContent = { ...props.content, features: newFeatures }
    emit('update:content', newContent)
  }
}

const openIconModal = (index: number) => {
  editingFeatureIndex.value = index
  isIconModalOpen.value = true
}

const onIconSave = (newIcon: string) => {
  if (editingFeatureIndex.value !== null) {
    const newFeatures = [...props.content.features]
    newFeatures[editingFeatureIndex.value] = {
      ...newFeatures[editingFeatureIndex.value],
      icon: newIcon,
    }
    const newContent = { ...props.content, features: newFeatures }
    emit('update:content', newContent)
  }
  isIconModalOpen.value = false
  editingFeatureIndex.value = null
}
</script>

<template>
  <!-- Hero -->
  <section class="relative py-12 bg-white sm:py-16 lg:py-20 overflow-hidden">
    <!-- Animated background accent -->
    <div
      class="absolute -top-40 left-1/2 -translate-x-1/2 w-[700px] h-[700px] bg-amber-200/50 rounded-full blur-3xl animate-pulse-slow z-0"
    ></div>
    <div class="absolute inset-0 z-0">
      <img class="object-cover w-full h-full" src="@/assets/images/grid-pattern.png" alt="" />
    </div>

    <!-- Hero content -->
    <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl z-10">
      <div class="max-w-5xl mx-auto text-center px-4">
        <h1 class="text-3xl font-medium text-gray-900 sm:text-5xl lg:text-7xl">
          <span class="relative group">
            <Transition name="slide-up" mode="out-in">
              <span :key="currentPhrase" class="text-amber-500 font-semibold block py-2">
                {{ currentPhrase }}
              </span>
            </Transition>
            <EditButton :is-admin="isAdmin" @edit="isPhrasesModalOpen = true" />
          </span>
          <span ref="titleRef" @blur="onTextBlur('title', $event)" class="relative group">
            {{ content.title }}
            <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
          </span>
        </h1>
        <div class="relative group max-w-md mx-auto">
          <p
            ref="subtitleRef"
            class="mt-6 text-base font-normal leading-7 text-gray-500"
            @blur="onTextBlur('subtitle', $event)"
          >
            {{ content.subtitle }}
          </p>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(subtitleRef)" />
        </div>

        <ul
          class="flex flex-col gap-2 sm:flex-row items-center justify-center mt-6 space-x-6 sm:space-x-8"
        >
          <li
            v-for="(feature, idx) in content.features"
            :key="idx"
            class="flex items-center relative group"
          >
            <div class="relative">
              <svg
                class="w-5 h-5 mr-2 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  :d="feature.icon"
                ></path>
              </svg>
              <EditButton :is-admin="isAdmin" @edit="openIconModal(idx)" />
            </div>
            <span
              :ref="(el) => (featureRefs[idx] = el as HTMLElement)"
              class="text-xs font-medium text-gray-900 sm:text-sm"
              @blur="onFeatureBlur(idx, $event)"
              >{{ feature.text }}</span
            >
            <EditButton :is-admin="isAdmin" @edit="makeEditable(featureRefs[idx])" />
          </li>
        </ul>
      </div>
    </div>

    <!-- Treatment cards -->
    <div
      class="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 gap-5 pb-8 mt-12 sm:mt-16 lg:mt-20 px-4"
    >
      <template v-for="(category, idx) in categories" :key="category.id">
        <RouterLink
          :to="{ name: 'Treatments', params: { category_slug: category.slug } }"
          :class="[
            // base card styles
            'relative rounded-2xl p-8 flex flex-col bg-gradient-to-br overflow-hidden group hover:shadow-md transition-all duration-300 border min-h-[250px]',
            category.bgClass,
            // sizing: max 2 per row, make one featured when odd
            isOdd && idx === featuredIndex ? 'sm:col-span-2' : '',
          ]"
        >
          <h3 :class="`${category.textClass} text-2xl font-bold mb-2`">{{ category.name }}</h3>
          <p v-if="category.description" :class="`${category.textClass} text-base mb-2`">
            {{ category.description }}
          </p>
          <div class="relative flex-1">
            <img
              :src="category.image"
              :alt="category.name"
              class="w-20 h-20 object-contain select-none pointer-events-none absolute left-0 bottom-0 lg:w-24 lg:h-24 lg:left-2 lg:-bottom-5 group-hover:scale-110 transition-transform duration-300"
            />
          </div>
          <div
            class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10"
          >
            Get Started
            <IconArrowRight
              class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all"
            />
          </div>
        </RouterLink>
      </template>
    </div>
    <PhrasesEditModal
      :is-open="isPhrasesModalOpen"
      :phrases="content.phrases"
      @close="isPhrasesModalOpen = false"
      @save="onPhrasesSave"
    />
    <IconEditModal
      :is-open="isIconModalOpen"
      :icon="editingFeatureIndex !== null ? content.features[editingFeatureIndex].icon : ''"
      @close="isIconModalOpen = false"
      @save="onIconSave"
    />
  </section>
</template>

<style scoped>
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
