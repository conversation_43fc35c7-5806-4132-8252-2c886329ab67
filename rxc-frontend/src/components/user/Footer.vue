<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import {
  IconBrandFacebook,
  IconBrandGmail,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandThreads,
  IconBrandTiktok,
  IconBrandX,
  IconBrandYoutube,
  type Icon as TablerIcon,
} from '@tabler/icons-vue'
import { useGlobalSettingsStore } from '@/stores'
import EditButton from '@/components/user/EditButton.vue'
import ObjectEditModal from '@/components/user/ObjectEditModal.vue'
import LinksEditModal from '@/components/user/LinksEditModal.vue'
import SocialLinksEditModal from '@/components/user/SocialLinksEditModal.vue'
import PolicyEditModal from '@/components/user/PolicyEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const store = useGlobalSettingsStore()
const { settings } = storeToRefs(store)

const iconComponents: Record<string, TablerIcon> = {
  IconBrandFacebook,
  IconBrandGmail,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandThreads,
  IconBrandTiktok,
  IconBrandX,
  IconBrandYoutube,
}

// Modals state
const isQuickLinksModalOpen = ref(false)
const isFollowUsModalOpen = ref(false)
const isPolicyModalOpen = ref(false)
const isContactModalOpen = ref(false)

function updateContent(newData: any) {
  emit('update:content', { ...props.content, ...newData })
}

function saveQuickLinks(links: any) {
  updateContent({ quickLinks: { ...props.content.quickLinks, links } })
}

function saveFollowUs(links: any) {
  updateContent({ followUs: { ...props.content.followUs, links } })
}

function savePolicy(data: any) {
  updateContent({ policy: data })
}

function saveContact(data: any) {
  updateContent({ contact: { ...props.content.contact, ...data } })
}
</script>

<template>
  <!-- Footer -->
  <footer
    class="bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 border-t border-neutral-800/60 pt-20 pb-10 w-full"
  >
    <div v-if="content" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 relative z-10 mb-12">
        <div class="relative group">
          <EditButton :is-admin="isAdmin" @edit="isQuickLinksModalOpen = true" />
          <h3 class="text-neutral-200 text-base font-semibold mb-4">
            {{ content.quickLinks.title }}
          </h3>
          <ul class="space-y-3">
            <li v-for="(link, index) in content.quickLinks.links" :key="index">
              <RouterLink
                :to="link.href"
                class="text-neutral-300 hover:text-amber-400 transition-colors"
                >{{ link.title }}</RouterLink
              >
            </li>
          </ul>
        </div>
        <div class="relative group">
          <EditButton :is-admin="isAdmin" @edit="isFollowUsModalOpen = true" />
          <h3 class="text-neutral-200 text-base font-semibold mb-4">
            {{ content.followUs.title }}
          </h3>
          <ul class="space-y-3">
            <li
              v-for="(link, index) in content.followUs.links"
              :key="index"
              class="flex items-center gap-2"
            >
              <component :is="iconComponents[link.icon]" class="w-5 h-5 text-neutral-300" />
              <a
                :href="link.href"
                class="text-neutral-300 hover:text-amber-400 transition-colors"
                >{{ link.title }}</a
              >
            </li>
          </ul>
        </div>
        <div class="relative group">
          <EditButton :is-admin="isAdmin" @edit="isContactModalOpen = true" />
          <h3 class="text-neutral-200 text-base font-semibold mb-4">
            {{ content.contact.title }}
          </h3>
          <div class="mb-2 text-neutral-400 text-sm">
            {{ content.contact.emailPrefix }}
          </div>
          <div class="mb-4">
            <a
              :href="`mailto:${content.contact.email}`"
              class="text-neutral-100 font-medium hover:text-amber-400 transition-colors inline-block"
            >
              {{ content.contact.email }}
            </a>
          </div>
          <div class="mb-2 text-neutral-400 text-sm">
            {{ content.contact.phonePrefix }}
          </div>
          <a
            :href="`tel:${content.contact.phone}`"
            class="text-neutral-100 font-medium hover:text-amber-400 transition-colors inline-block"
          >
            {{ content.contact.phone }}
          </a>
        </div>
      </div>
      <!-- Big Brand Name -->
      <div class="w-full flex flex-col items-center mb-12">
        <span
          class="text-[12vw] font-extrabold text-neutral-700/40 tracking-tighter text-center leading-none select-none"
          >{{ settings?.app_name }}</span
        >
      </div>
      <div class="border-t border-neutral-700 pt-8 mt-8">
        <div class="flex flex-wrap justify-center items-center gap-8 mb-6">
          <img
            src="@/assets/images/hipaa-compliant.png"
            alt="HIPAA Compliant"
            class="h-12 w-auto object-contain"
          />
          <img
            src="@/assets/images/legit-script.png"
            alt="LegitScript Certified"
            class="h-12 w-auto object-contain"
          />
          <img
            src="@/assets/images/secure.png"
            alt="Secure & Encrypted"
            class="h-12 w-auto object-contain"
          />
        </div>
        <div
          class="flex flex-col md:flex-row justify-center items-center gap-4 text-neutral-500 text-xs relative group"
        >
          <EditButton :is-admin="isAdmin" @edit="isPolicyModalOpen = true" />
          <template v-for="(link, index) in content.policy.links" :key="index">
            <a :href="link.href" target="_blank" class="hover:text-amber-400 transition-colors">{{
              link.title
            }}</a>
            <span v-if="index < content.policy.links.length - 1" class="hidden md:inline">|</span>
          </template>
          <span class="hidden md:inline">|</span>
          <div class="relative">
            <span>&copy; {{ content.policy.copyright }}</span>
          </div>
        </div>
      </div>

      <!-- Modals -->
      <LinksEditModal
        :is-open="isQuickLinksModalOpen"
        :links="content?.quickLinks.links"
        @close="isQuickLinksModalOpen = false"
        @save="saveQuickLinks"
      />
      <SocialLinksEditModal
        :is-open="isFollowUsModalOpen"
        :links="content?.followUs.links"
        @close="isFollowUsModalOpen = false"
        @save="saveFollowUs"
      />
      <PolicyEditModal
        :is-open="isPolicyModalOpen"
        :data="content?.policy"
        @close="isPolicyModalOpen = false"
        @save="savePolicy"
      />
      <ObjectEditModal
        :is-open="isContactModalOpen"
        :data="content?.contact"
        @close="isContactModalOpen = false"
        @save="saveContact"
        title="Edit Contact Info"
      />
    </div>
  </footer>
</template>
