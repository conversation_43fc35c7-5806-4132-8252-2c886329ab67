import './assets/style.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useGlobalSettingsStore } from '@/stores'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// preload app settings before mount
const settingsStore = useGlobalSettingsStore()
await settingsStore.loadSettings()

app.mount('#app')
