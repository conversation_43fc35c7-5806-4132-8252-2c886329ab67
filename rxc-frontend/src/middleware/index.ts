import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import type { Middleware } from './types'

export function defineMiddleware(middleware: Middleware | Middleware[]) {
  return async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext,
  ) => {
    const middlewareList = Array.isArray(middleware) ? middleware : [middleware]

    try {
      for (const middlewareFn of middlewareList) {
        await middlewareFn({ to, from, next })
      }
    } catch (error) {
      console.error('Middleware error:', error)
      next(false)
    }
  }
}
