import { useAuthStore } from '@/stores'
import type { Middleware } from './types'

export const auth: Middleware = async ({ to, next }) => {
  const authStore = useAuthStore()

  if (!authStore.isAuthenticated) {
    return next({
      name: 'admin-login',
      query: { redirect: to.fullPath },
    })
  }

  // Verify token if authenticated
  const isValid = await authStore.verifyToken()

  if (!isValid) {
    return next({
      name: 'admin-login',
      query: { redirect: to.fullPath },
    })
  }

  return next()
}

export const guest: Middleware = ({ next }) => {
  const authStore = useAuthStore()

  if (authStore.isAuthenticated) {
    return next({ name: 'admin-dashboard' })
  }

  return next()
}
