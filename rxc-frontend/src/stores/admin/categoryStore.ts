import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'

// types
export type Category = {
  id: string
  name: string
  description?: string
  slug: string
  image: string
  is_active: 0 | 1
  gender: string
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string

  // custom field
  status: boolean
}

export type CategoryDetail = Pick<
  Category,
  'id' | 'name' | 'slug' | 'image' | 'is_active' | 'gender' | 'description'
>

export type CategoriesListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type CategoriesListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: Category[]
}

export type CategoryPayload = {
  name: string
  description?: string
  gender: string
  image: string
}

// store definition
export const useCategoryStore = defineStore('categoryStore', () => {
  // state
  const categories = ref<Category[]>([])
  const selectedCategory = ref<CategoryDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isUpdatingImg = ref(false)
  const isDeleting = ref(false)
  const isTogglingStatus = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  // getters
  const listPayload = computed<Partial<CategoriesListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
  }))

  // actions
  async function fetchCategories() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { mainCategories: CategoriesListResponse }
      >('/admin/main-category/list', listPayload.value)

      if (response.data.status === 200) {
        categories.value = response.data.mainCategories.records.map((category) => ({
          ...category,
          status: Boolean(category.is_active),
        }))

        pagination.value = {
          currentPage: response.data.mainCategories.current_page,
          perPage: response.data.mainCategories.per_page,
          totalPages: response.data.mainCategories.totalPage,
          totalRecords: response.data.mainCategories.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch categories'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching categories:', err)
      error.value = processErrors(err, 'An error occurred while fetching categories.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getCategoryById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { mainCategory: CategoryDetail }>(
        `/admin/main-category/view/${id}`,
      )

      if (response.data.status === 200) {
        selectedCategory.value = response.data.mainCategory
        return true
      } else {
        error.value = response.data.message || `Failed to fetch category with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching category details:', err)
      error.value = processErrors(err, 'An error occurred while fetching category details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addCategory(payload: CategoryPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/main-category/add', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Category added successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add category'
        return false
      }
    } catch (err: any) {
      console.error('Error adding category:', err)
      error.value = processErrors(err, 'An error occurred while adding category.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updateCategory(payload: Pick<Category, 'id' | 'name' | 'description' | 'gender'>) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/main-category/edit', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Category updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update category'
        return false
      }
    } catch (err: any) {
      console.error('Error updating category:', err)
      error.value = processErrors(err, 'An error occurred while updating category.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function updateCategoryImg(payload: { id: string; image: string }) {
    isUpdatingImg.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>(
        '/admin/update-main-category-image',
        payload,
      )

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Category image updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update category image'
        return false
      }
    } catch (err: any) {
      console.error('Error updating category image:', err)
      error.value = processErrors(err, 'An error occurred while updating category image.')
      return false
    } finally {
      isUpdatingImg.value = false
    }
  }

  async function toggleCategoryStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/main-category/update/status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Category status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update category status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating category status:', err)
      error.value = processErrors(err, 'An error occurred while updating category status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function deleteCategory(id: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.delete<ApiResponse>(`/admin/main-category/delete/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Category deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete category'
        return false
      }
    } catch (err: any) {
      console.error('Error deleting category:', err)
      error.value = processErrors(err, 'An error occurred while deleting category.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  return {
    // State
    categories,
    selectedCategory,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isUpdatingImg,
    isTogglingStatus,
    isDeleting,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    sortBy,

    // Actions
    fetchCategories,
    getCategoryById,
    addCategory,
    updateCategory,
    updateCategoryImg,
    toggleCategoryStatus,
    deleteCategory,
  }
})
