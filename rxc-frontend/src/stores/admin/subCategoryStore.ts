import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'

// types
export type SubCategory = {
  id: string
  name: string
  slug: string
  is_active: 0 | 1
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string
  main_category_id: string
  main_category: {
    id: string
    name: string
    image: string
  }

  // custom field
  status: boolean
}

export type SubCategoryDetail = Pick<
  SubCategory,
  'id' | 'name' | 'slug' | 'is_active' | 'main_category_id' | 'main_category'
>

export type SubCategoriesListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type SubCategoriesListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: SubCategory[]
}

export type SubCategoryPayload = {
  main_category_id: string
  name: string
}

export type CategoryOption = {
  id: string
  name: string
  gender: string
}

// store definition
export const useSubCategoryStore = defineStore('subCategoryStore', () => {
  // state
  const subCategories = ref<SubCategory[]>([])
  const selectedSubCategory = ref<SubCategoryDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isUpdatingImg = ref(false)
  const isDeleting = ref(false)
  const isTogglingStatus = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  const categoryOptions = ref<CategoryOption[]>([])
  const isLoadingCategoryOptions = ref(false)

  // getters
  const listPayload = computed<Partial<SubCategoriesListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
  }))

  // actions
  async function fetchSubCategories() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { subCategories: SubCategoriesListResponse }
      >('/admin/sub-category/list', listPayload.value)

      if (response.data.status === 200) {
        subCategories.value = response.data.subCategories.records.map((item) => ({
          ...item,
          status: Boolean(item.is_active),
        }))

        pagination.value = {
          currentPage: response.data.subCategories.current_page,
          perPage: response.data.subCategories.per_page,
          totalPages: response.data.subCategories.totalPage,
          totalRecords: response.data.subCategories.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch sub-categories'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching sub-categories:', err)
      error.value = processErrors(err, 'An error occurred while fetching sub-categories.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getSubCategoryById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { subCategory: SubCategoryDetail }>(
        `/admin/sub-category/view/${id}`,
      )

      if (response.data.status === 200) {
        selectedSubCategory.value = response.data.subCategory
        return true
      } else {
        error.value = response.data.message || `Failed to fetch sub-category with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching sub-category details:', err)
      error.value = processErrors(err, 'An error occurred while fetching sub-category details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addSubCategory(payload: SubCategoryPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/sub-category/add', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub-category added successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add sub-category'
        return false
      }
    } catch (err: any) {
      console.error('Error adding sub-category:', err)
      error.value = processErrors(err, 'An error occurred while adding sub-category.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updateSubCategory(payload: { id: string; name: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/sub-category/edit', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub-category updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update sub-category'
        return false
      }
    } catch (err: any) {
      console.error('Error updating sub-category:', err)
      error.value = processErrors(err, 'An error occurred while updating sub-category.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function toggleSubCategoryStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/sub-category/update/status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub-category status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update sub-category status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating sub-category status:', err)
      error.value = processErrors(err, 'An error occurred while updating sub-category status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function deleteSubCategory(id: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.delete<ApiResponse>(`/admin/sub-category/delete/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub-category deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete sub-category'
        return false
      }
    } catch (err: any) {
      console.error('Error deleting sub-category:', err)
      error.value = processErrors(err, 'An error occurred while deleting sub-category.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  const fetchCategoryOptions = async () => {
    isLoadingCategoryOptions.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { mainCategories: CategoryOption[] }>(
        '/admin/main-category/all',
      )

      if (response.data.status === 200 && Array.isArray(response.data.mainCategories)) {
        categoryOptions.value = response.data.mainCategories
        return true
      } else {
        error.value = response.data.message || 'Failed to load categories'
        return false
      }
    } catch (err) {
      console.error('Error fetching categories:', err)
      error.value = processErrors(err, 'An error occurred while fetching categories')
      return false
    } finally {
      isLoadingCategoryOptions.value = false
    }
  }

  return {
    // State
    subCategories,
    selectedSubCategory,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isUpdatingImg,
    isTogglingStatus,
    isDeleting,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    sortBy,
    categoryOptions,
    isLoadingCategoryOptions,

    // Actions
    fetchSubCategories,
    getSubCategoryById,
    addSubCategory,
    updateSubCategory,
    toggleSubCategoryStatus,
    deleteSubCategory,
    fetchCategoryOptions,
  }
})
