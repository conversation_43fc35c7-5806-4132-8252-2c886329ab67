import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import { z } from 'zod'

// types
export type Product = {
  id: string
  slug: string
  image: string
  is_active: 0 | 1
  main_category_name: string
  sub_category_name: string | null
  product_type: 'single' | 'programs'
  product_name_title: string | null
  product_form: string
  product_strength: string
  product_qty: string
  product_price: number
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string

  // custom field
  status: boolean
}

export type ProductDetail = {
  id: string
  main_category_id: string
  sub_category_id: string | null
  product_type: 'single' | 'programs'
  product_title: string | null
  slug: string
  image: string
  is_active: 0 | 1
  main_category_name: string
  sub_category_name: string | null
  product_items: {
    id: string
    product_id: string
    product_name: string
    product_form: string
    product_strength: string
    product_qty: string
    price: number
    xpedicare_url: string
  }[]
  product_descriptions: {
    title: string
    description: string
  }[]
  product_faqs: {
    question: string
    answer: string
  }[]
}

export type ProductListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type ProductListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: Product[]
}

type CategoryOption = {
  id: string
  name: string
  gender: string
  sub_categories: {
    id: string
    name: string
    main_category_id: string
  }[]
}

const baseProductSchema = z.object({
  main_category_id: z.string().min(1, 'Main category is required'),
  sub_category_id: z.string().optional(),
  image: z.string().min(1, 'Image is required'),
})

const singleProductSchema = baseProductSchema.extend({
  product_type: z.literal('single'),
  product_name: z.string().min(1, 'Product name is required'),
  product_qty: z.string().optional(),
  product_form: z.string().optional(),
  product_strength: z.string().optional(),
  price: z.string().min(1, 'Price is required'),
  xpedicare_url: z.string().url('Invalid URL'),
})

const programItemSchema = z.object({
  product_name: z.string().min(1, 'Product name is required'),
  product_qty: z.string().optional(),
  product_form: z.string().optional(),
  product_strength: z.string().optional(),
  price: z.string().min(1, 'Price is required'),
  xpedicare_url: z.string().url('Invalid URL'),
})

const programProductSchema = baseProductSchema.extend({
  product_type: z.literal('programs'),
  product_title: z.string().min(1, 'Program title is required'),
  product_items: z.array(programItemSchema).min(1, 'At least one product item is required'),
})

export const productPayloadSchema = z.discriminatedUnion('product_type', [
  singleProductSchema,
  programProductSchema,
])

export const productOverviewSchema = z.object({
  product_descriptions: z
    .array(
      z.object({
        title: z.string().min(1, 'Section Title is required'),
        description: z.string().min(1, 'Section Description is required'),
      }),
    )
    .optional(),
  product_faqs: z
    .array(
      z.object({
        question: z.string().min(1, 'Question is required'),
        answer: z.string().min(1, 'Answer is required'),
      }),
    )
    .optional(),
})

export type ProductPayload = z.infer<typeof productPayloadSchema>
export type ProductOverviewPayload = z.infer<typeof productOverviewSchema>

// store definition
export const useProductStore = defineStore('productStore', () => {
  // state
  const products = ref<Product[]>([])
  const selectedProduct = ref<ProductDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isUpdatingImg = ref(false)
  const isDeleting = ref(false)
  const isUpdatingOverview = ref(false)
  const isTogglingStatus = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  const categoryOptions = ref<CategoryOption[]>([])
  const isLoadingCategoryOptions = ref(false)

  // getters
  const listPayload = computed<Partial<ProductListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
  }))

  // actions
  async function fetchProducts() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { products: ProductListResponse }>(
        '/admin/product/list',
        listPayload.value,
      )

      if (response.data.status === 200) {
        products.value = response.data.products.records.map((item) => ({
          ...item,
          status: Boolean(item.is_active),
        }))

        pagination.value = {
          currentPage: response.data.products.current_page,
          perPage: response.data.products.per_page,
          totalPages: response.data.products.totalPage,
          totalRecords: response.data.products.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch products'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching products:', err)
      error.value = processErrors(err, 'An error occurred while fetching products.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getProductById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { productDetails: ProductDetail }>(
        `/admin/product/view/${id}`,
      )

      if (response.data.status === 200) {
        selectedProduct.value = response.data.productDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch product with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching product details:', err)
      error.value = processErrors(err, 'An error occurred while fetching product details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addProduct(payload: ProductPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse & { id: string }>(
        '/admin/product/add',
        payload,
      )

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Product added successfully')
        return { success: true, id: response.data.id }
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return { success: false }
      } else {
        error.value = response.data.message || 'Failed to add product'
        return { success: false }
      }
    } catch (err: any) {
      console.error('Error adding product:', err)
      error.value = processErrors(err, 'An error occurred while adding product.')
      return { success: false }
    } finally {
      isAdding.value = false
    }
  }

  async function updateProduct(payload: ProductPayload & { id: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/product/edit', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Product updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update product'
        return false
      }
    } catch (err: any) {
      console.error('Error updating product:', err)
      error.value = processErrors(err, 'An error occurred while updating product.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function updateProductImg(payload: { id: string; image: string }) {
    isUpdatingImg.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-product-image', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Product image updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update product image'
        return false
      }
    } catch (err: any) {
      console.error('Error updating product image:', err)
      error.value = processErrors(err, 'An error occurred while updating product image.')
      return false
    } finally {
      isUpdatingImg.value = false
    }
  }

  async function toggleProductStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/product/update/status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Product status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update product status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating product status:', err)
      error.value = processErrors(err, 'An error occurred while updating product status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function updateProductOverview(payload: ProductOverviewPayload & { id: string }) {
    isUpdatingOverview.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>(
        '/admin/update-product-descriptions-faqs',
        payload,
      )

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Product overview updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update product overview'
        return false
      }
    } catch (err: any) {
      console.error('Error updating product overview:', err)
      error.value = processErrors(err, 'An error occurred while updating product overview.')
      return false
    } finally {
      isUpdatingOverview.value = false
    }
  }

  async function deleteProduct(id: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.delete<ApiResponse>(`/admin/product/delete/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Product deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete product'
        return false
      }
    } catch (err: any) {
      console.error('Error deleting product:', err)
      error.value = processErrors(err, 'An error occurred while deleting product.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  const fetchCategoryOptions = async () => {
    isLoadingCategoryOptions.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { allCategories: CategoryOption[] }>(
        '/admin/all-categories',
      )

      if (response.data.status === 200 && Array.isArray(response.data.allCategories)) {
        categoryOptions.value = response.data.allCategories
        return true
      } else {
        error.value = response.data.message || 'Failed to load categories'
        return false
      }
    } catch (err) {
      console.error('Error fetching categories:', err)
      error.value = processErrors(err, 'An error occurred while fetching categories')
      return false
    } finally {
      isLoadingCategoryOptions.value = false
    }
  }

  return {
    // State
    products,
    selectedProduct,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isUpdatingImg,
    isTogglingStatus,
    isUpdatingOverview,
    isDeleting,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    sortBy,
    categoryOptions,
    isLoadingCategoryOptions,

    // Actions
    fetchProducts,
    getProductById,
    addProduct,
    updateProduct,
    updateProductImg,
    updateProductOverview,
    toggleProductStatus,
    deleteProduct,
    fetchCategoryOptions,
  }
})
