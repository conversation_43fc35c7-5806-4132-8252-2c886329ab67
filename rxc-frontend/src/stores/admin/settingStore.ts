import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import type { ApiResponse, AppSettings } from '@/types/api'
import { processErrors } from '@/lib'
import { z } from 'zod'
import { toast } from 'vue-sonner'

const urlOrEmpty = z.union([z.string().url('Invalid URL'), z.literal('')])
const emailOrEmpty = z.union([z.string().email('Invalid email'), z.literal('')])
const phoneOrEmpty = z.union([z.string().min(10, 'Invalid phone number'), z.literal('')])

export const appSettingSchema = z.object({
  app_name: z.string().min(1, 'App name is required'),
  terms_and_conditions_url: urlOrEmpty.optional(),
  privacy_policy_url: urlOrEmpty.optional(),
  instagram_url: urlOrEmpty.optional(),
  facebook_url: urlOrEmpty.optional(),
  twitter_url: urlOrEmpty.optional(),
  linkedin_url: urlOrEmpty.optional(),
  youtube_url: urlOrEmpty.optional(),
  tiktok_url: urlOrEmpty.optional(),
  support_email: emailOrEmpty.optional(),
  support_phone_number: phoneOrEmpty.optional(),
  info_email: emailOrEmpty.optional(),
})

export const useSettingsStore = defineStore('settings', () => {
  const settings = ref<AppSettings>({})
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const isUpdatingAppSetting = ref(false)
  const isUpdatingLogo = ref(false)
  const isUpdatingFavicon = ref(false)

  const fetchSettings = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & {
          app_settings: AppSettings
        }
      >('/admin/app-settings')

      if (response.data.status === 200 && response.data.app_settings) {
        settings.value = response.data.app_settings
      }
    } catch (err) {
      error.value = processErrors(err)[0]
    } finally {
      isLoading.value = false
    }
  }

  async function updateAppSetting(payload: z.infer<typeof appSettingSchema>) {
    isUpdatingAppSetting.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-settings', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'App settings updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update app settings'
        return false
      }
    } catch (err: any) {
      console.error('Error updating app settings:', err)
      error.value = processErrors(err, 'An error occurred while updating app settings.')
      return false
    } finally {
      isUpdatingAppSetting.value = false
    }
  }

  async function updateLogo(payload: { image: string }) {
    isUpdatingLogo.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-logo', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'App logo updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update app logo'
        return false
      }
    } catch (err: any) {
      console.error('Error updating app logo:', err)
      error.value = processErrors(err, 'An error occurred while updating app logo.')
      return false
    } finally {
      isUpdatingLogo.value = false
    }
  }

  async function updateFavicon(payload: { image: string }) {
    isUpdatingFavicon.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-favicon', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Favicon updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update favicon'
        return false
      }
    } catch (err: any) {
      console.error('Error updating favicon:', err)
      error.value = processErrors(err, 'An error occurred while updating favicon.')
      return false
    } finally {
      isUpdatingFavicon.value = false
    }
  }

  return {
    // state
    settings,
    error,
    fieldErrors,
    isLoading,
    isUpdatingAppSetting,
    isUpdatingLogo,
    isUpdatingFavicon,

    // actions
    fetchSettings,
    updateAppSetting,
    updateLogo,
    updateFavicon,
  }
})
