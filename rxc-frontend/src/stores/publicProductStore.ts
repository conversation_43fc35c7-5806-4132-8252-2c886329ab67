import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient } from '@/composables'
import type {
  ApiResponse,
  PaginationResponse,
  Product,
  Category,
  ProductDetails,
} from '@/types/api'
import { processErrors } from '@/lib'

type ProductListPayload = {
  page?: number
  perPage?: number
  slug?: string
  searchQuery?: string
}

export const usePublicProductStore = defineStore('publicProducts', () => {
  // products
  const products = ref<Product[]>([])
  const isLoadingList = ref(false)
  const listError = ref<string | null>(null)
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const slug = ref<string | undefined>()
  const listPayload = computed<ProductListPayload>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    slug: slug.value,
  }))

  async function fetchProducts() {
    isLoadingList.value = true
    listError.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { products: PaginationResponse<Product> }
      >('/get-products', listPayload.value)

      if (response.data.status === 200) {
        const pagedData = response.data.products
        products.value = pagedData.records
        pagination.value = {
          currentPage: pagedData.current_page,
          perPage: pagedData.per_page,
          totalPages: pagedData.totalPage,
          totalRecords: pagedData.totalRecords,
        }

        return true
      } else {
        listError.value = response.data.message || 'Failed to fetch products'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching products:', err)
      listError.value = processErrors(err, 'An error occurred while fetching products.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  // product details
  const productDetails = ref<ProductDetails | null>(null)
  const isLoadingDetails = ref(false)
  const detailsError = ref<string | null>(null)

  async function fetchProductDetails(productSlug: string) {
    isLoadingDetails.value = true
    productDetails.value = null
    detailsError.value = null

    try {
      const { data } = await apiClient.get<ApiResponse & { productDetails: ProductDetails }>(
        `/get-product-details/${productSlug}`,
      )
      if (data.status === 200) {
        productDetails.value = data.productDetails
        return true
      } else {
        detailsError.value = data.message || 'Failed to load product details'
        return false
      }
    } catch (err: any) {
      detailsError.value = processErrors(err, 'Failed to load product details')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  // categories
  const categories = ref<Category[]>([])
  const isLoadingCategories = ref(false)
  const categoryError = ref<string | null>(null)

  async function fetchAllCategories() {
    isLoadingCategories.value = true
    try {
      const { data } = await apiClient.get<ApiResponse & { categories: Category[] }>(
        '/fetch-all-categories',
      )
      if (data.status === 200) {
        categories.value = data.categories
      }
    } catch (err) {
      console.error('Error fetching categories:', err)
      categoryError.value = processErrors(err, 'Failed to fetch categories')
    } finally {
      isLoadingCategories.value = false
    }
  }

  return {
    // products
    products,
    isLoadingList,
    pagination,
    searchQuery,
    slug,
    listError,
    fetchProducts,

    // product details
    productDetails,
    isLoadingDetails,
    detailsError,
    fetchProductDetails,

    // categories
    categories,
    isLoadingCategories,
    categoryError,
    fetchAllCategories,
  }
})
