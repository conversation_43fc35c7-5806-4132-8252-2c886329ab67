<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppSidebar from '@/components/AppSidebar.vue'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { Button } from '@/components/ui/button'
import { IconArrowRight } from '@tabler/icons-vue'

const route = useRoute()

const pageTitle = computed(() => {
  return route.meta.title || 'WhiteLblRX Admin'
})

const parentTitle = computed(() => {
  return route.meta.parent || null
})

const parentRouteName = computed(() => {
  return route.meta.parentRouteName || null
})
</script>

<template>
  <div class="flex min-h-screen w-full">
    <SidebarProvider>
      <AppSidebar />
      <div class="flex-1 flex flex-col min-w-0">
        <SidebarInset>
          <header
            class="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12"
          >
            <div class="flex items-center justify-between gap-2 px-4 w-full">
              <div class="flex items-center gap-2">
                <SidebarTrigger class="-ml-1" />
                <Separator orientation="vertical" class="mr-2 h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem v-if="parentTitle" class="hidden md:block">
                      <RouterLink
                        v-if="parentRouteName"
                        :to="{ name: parentRouteName as string }"
                        class="hover:underline"
                      >
                        {{ parentTitle }}
                      </RouterLink>
                      <BreadcrumbLink v-else>{{ parentTitle }}</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator v-if="parentTitle" class="hidden md:block" />
                    <BreadcrumbItem>
                      <BreadcrumbPage class="font-semibold">
                        {{ pageTitle }}
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
              <div class="flex items-center gap-3">
                <RouterLink :to="{ name: 'EditMainSite' }" target="_blank">
                  <Button variant="outline">Edit Main Site <IconArrowRight /></Button>
                </RouterLink>
                <ThemeToggle />
              </div>
            </div>
          </header>
          <main class="flex-1 overflow-auto p-4">
            <div class="max-w-full">
              <router-view />
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  </div>
</template>
