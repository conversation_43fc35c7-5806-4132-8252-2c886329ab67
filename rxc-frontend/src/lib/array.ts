/**
 * Returns an array with unique values from the input array.
 *
 * @param arr - The array to process.
 * @returns An array with unique values.
 */
export function unique<T>(arr: T[]): T[] {
  return [...new Set(arr)]
}

/**
 * Splits an array into chunks of a specified size.
 *
 * @param arr - The array to split.
 * @param size - The size of each chunk.
 * @returns An array of chunks.
 */
export function chunkArray<T>(arr: T[], size: number): T[][] {
  const result: T[][] = []
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size))
  }
  return result
}
