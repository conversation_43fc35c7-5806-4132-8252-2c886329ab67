/**
 * Formats a number as a currency string.
 *
 * @param amount - The amount to format.
 * @param currency - The currency code to use. Defaults to 'USD'.
 * @param locale - The locale to use for formatting. Defaults to 'en-US'.
 * @returns A formatted currency string.
 */
export function formatCurrency(amount: number, currency = 'USD', locale = 'en-US'): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount)
}
