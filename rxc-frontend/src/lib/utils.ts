import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { toast } from 'vue-sonner'

/**
 * Merges multiple class values into a single string.
 *
 * @param inputs - An array of class values to merge.
 * @returns A string containing all the merged class values.
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function processErrors(
  errorData: any,
  defaultError = 'An unexpected error occurred. Please try again later.',
) {
  if (errorData.errors && typeof errorData.errors === 'object') {
    for (const key in errorData.errors) {
      return String(errorData.errors[key][0])
    }
  } else if (errorData.response && errorData.response.data && errorData.response.data.message) {
    return String(errorData.response.data.message)
  } else if (errorData.message) {
    return String(errorData.message)
  }

  return defaultError
}

export function scrollToTop() {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

export function copyLinkToClipboard(text: string) {
  navigator.clipboard.writeText(text).then(
    () => {
      toast.success(`Link copied to clipboard: '${text}'`)
    },
    (err) => {
      console.error('Could not copy text: ', err)
    },
  )
}
