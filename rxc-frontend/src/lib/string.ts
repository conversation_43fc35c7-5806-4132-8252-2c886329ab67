/**
 * Capitalize the first letter of a string.
 * @param {string} str
 * @return {string}
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * Truncate a string to the given length.
 * @param {string} str
 * @param {number} length
 * @return {string}
 */
export function truncate(str: string, length: number): string {
  return str.length > length ? str.slice(0, length) + '…' : str
}
