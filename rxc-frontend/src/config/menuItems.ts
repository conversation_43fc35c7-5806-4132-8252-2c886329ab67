import type { LucideIcon } from 'lucide-vue-next'
import { LayoutList, LayoutDashboard, Package, Settings2, Star } from 'lucide-vue-next'

export interface SubMenuItem {
  title: string
  routeName: string
}

export interface MenuItem {
  title: string
  routeName?: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SubMenuItem[]
}

export interface MenuGroup {
  label?: string
  items: MenuItem[]
}

export type MenuConfig = MenuGroup[]

export const menuItems: MenuConfig = [
  {
    label: 'Platform',
    items: [
      {
        title: 'Dashboard',
        routeName: 'admin-dashboard',
        icon: LayoutDashboard,
        isActive: true,
      },
      {
        title: 'Categories',
        icon: LayoutList,
        routeName: 'admin-categories',
      },
      {
        title: 'Sub-Categories',
        icon: LayoutList,
        routeName: 'admin-sub-categories',
      },
      {
        title: 'Products',
        icon: Package,
        routeName: 'admin-products',
      },
      {
        title: 'Top Products',
        icon: Star,
        routeName: 'admin-top-products',
      },
    ],
  },
  {
    label: 'Application',
    items: [
      {
        title: 'Settings',
        icon: Settings2,
        routeName: 'admin-settings',
      },
    ],
  },
]
